#!/usr/bin/env python3
"""
网络分析工具 - 深度分析BOSS直聘的API和网络请求
"""

import asyncio
import json
import re
from typing import List, Dict, Any
from urllib.parse import urlparse, parse_qs

from crawl4ai import Async<PERSON>ebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
from utils import Logger


class NetworkAnalyzer:
    """网络分析器"""
    
    def __init__(self):
        self.crawler = None
        self.intercepted_requests = []
        self.api_endpoints = []
        self.cookies = {}
        self.headers = {}
    
    async def __aenter__(self):
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
    
    async def initialize(self):
        """初始化分析器"""
        Logger.info("🔍 初始化网络分析器...")
        
        browser_config = BrowserConfig(
            headless=False,  # 显示浏览器便于观察
            viewport_width=1920,
            viewport_height=1080,
            java_script_enabled=True,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            extra_args=[
                "--disable-blink-features=AutomationControlled",
                "--disable-dev-shm-usage",
                "--no-sandbox"
            ]
        )
        
        self.crawler = AsyncWebCrawler(config=browser_config)
        await self.crawler.__aenter__()
        
        Logger.success("✅ 网络分析器初始化完成")
    
    async def close(self):
        """关闭分析器"""
        if self.crawler:
            await self.crawler.__aexit__(None, None, None)
            Logger.info("🔒 网络分析器已关闭")
    
    async def deep_network_analysis(self, target_url: str) -> Dict[str, Any]:
        """深度网络分析"""
        Logger.info(f"🕵️ 开始深度网络分析: {target_url}")
        
        # 高级网络拦截JavaScript
        network_intercept_js = """
        // 创建全局请求存储
        window.networkRequests = [];
        window.apiEndpoints = [];
        window.cookieData = {};
        window.headerData = {};
        
        // 拦截所有网络请求
        const originalFetch = window.fetch;
        const originalXHR = window.XMLHttpRequest;
        
        // 拦截fetch请求
        window.fetch = function(...args) {
            const url = args[0];
            const options = args[1] || {};
            
            const requestData = {
                type: 'fetch',
                url: url,
                method: options.method || 'GET',
                headers: options.headers || {},
                body: options.body,
                timestamp: Date.now()
            };
            
            window.networkRequests.push(requestData);
            
            // 分析是否为API请求
            if (url.includes('/api/') || url.includes('/wapi/') || url.includes('.json')) {
                window.apiEndpoints.push(requestData);
            }
            
            return originalFetch.apply(this, args).then(response => {
                // 记录响应信息
                requestData.status = response.status;
                requestData.statusText = response.statusText;
                
                // 尝试获取响应内容
                const clonedResponse = response.clone();
                clonedResponse.text().then(text => {
                    requestData.responseText = text;
                    
                    // 如果是JSON响应，尝试解析
                    try {
                        requestData.responseJson = JSON.parse(text);
                    } catch (e) {
                        // 不是JSON格式
                    }
                }).catch(() => {});
                
                return response;
            });
        };
        
        // 拦截XMLHttpRequest
        const XHROpen = originalXHR.prototype.open;
        const XHRSend = originalXHR.prototype.send;
        const XHRSetRequestHeader = originalXHR.prototype.setRequestHeader;
        
        originalXHR.prototype.open = function(method, url, ...args) {
            this._method = method;
            this._url = url;
            this._headers = {};
            return XHROpen.apply(this, [method, url, ...args]);
        };
        
        originalXHR.prototype.setRequestHeader = function(name, value) {
            this._headers[name] = value;
            return XHRSetRequestHeader.apply(this, [name, value]);
        };
        
        originalXHR.prototype.send = function(body) {
            const requestData = {
                type: 'xhr',
                url: this._url,
                method: this._method,
                headers: this._headers,
                body: body,
                timestamp: Date.now()
            };
            
            window.networkRequests.push(requestData);
            
            // 分析是否为API请求
            if (this._url.includes('/api/') || this._url.includes('/wapi/') || this._url.includes('.json')) {
                window.apiEndpoints.push(requestData);
            }
            
            // 监听响应
            this.addEventListener('readystatechange', function() {
                if (this.readyState === 4) {
                    requestData.status = this.status;
                    requestData.statusText = this.statusText;
                    requestData.responseText = this.responseText;
                    
                    // 尝试解析JSON响应
                    try {
                        requestData.responseJson = JSON.parse(this.responseText);
                    } catch (e) {
                        // 不是JSON格式
                    }
                }
            });
            
            return XHRSend.apply(this, [body]);
        };
        
        // 获取Cookie信息
        window.cookieData = {
            all: document.cookie,
            parsed: document.cookie.split(';').reduce((acc, cookie) => {
                const [key, value] = cookie.trim().split('=');
                if (key && value) {
                    acc[key] = value;
                }
                return acc;
            }, {})
        };
        
        // 获取常用Header信息
        window.headerData = {
            userAgent: navigator.userAgent,
            language: navigator.language,
            platform: navigator.platform,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine,
            referrer: document.referrer,
            location: window.location.href
        };
        
        // 模拟用户行为触发更多请求
        const simulateUserInteraction = async () => {
            // 滚动页面
            for (let i = 0; i < 5; i++) {
                window.scrollTo(0, (document.body.scrollHeight / 5) * i);
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            // 尝试点击搜索相关元素
            const searchElements = document.querySelectorAll('[class*="search"], [class*="job"], button, a');
            for (let i = 0; i < Math.min(3, searchElements.length); i++) {
                const element = searchElements[i];
                if (element.offsetParent !== null) {
                    // 模拟hover
                    const hoverEvent = new MouseEvent('mouseenter', { bubbles: true });
                    element.dispatchEvent(hoverEvent);
                    await new Promise(resolve => setTimeout(resolve, 500));
                    
                    // 模拟点击（但不实际执行）
                    const clickEvent = new MouseEvent('mouseover', { bubbles: true });
                    element.dispatchEvent(clickEvent);
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
            }
            
            // 等待更多网络请求
            await new Promise(resolve => setTimeout(resolve, 5000));
        };
        
        await simulateUserInteraction();
        
        console.log('网络分析完成，拦截到', window.networkRequests.length, '个请求');
        console.log('发现', window.apiEndpoints.length, '个API端点');
        """
        
        run_config = CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            page_timeout=180000,  # 3分钟
            delay_before_return_html=20.0,  # 等待20秒
            js_code=[network_intercept_js]
        )
        
        try:
            result = await self.crawler.arun(url=target_url, config=run_config)
            
            if result.success:
                Logger.success("✅ 页面访问成功，开始提取网络数据...")
                
                # 获取拦截的网络请求
                network_data = await self._extract_network_data()
                
                # 分析和保存结果
                analysis_result = self._analyze_network_data(network_data)
                
                # 保存分析结果
                await self._save_analysis_result(analysis_result)
                
                return analysis_result
            else:
                Logger.error(f"❌ 页面访问失败: {result.error_message}")
                return {}
                
        except Exception as e:
            Logger.error(f"💥 网络分析异常: {e}")
            return {}
    
    async def _extract_network_data(self) -> Dict[str, Any]:
        """提取网络数据"""
        try:
            # 获取拦截的请求数据
            requests_js = "return window.networkRequests || [];"
            api_js = "return window.apiEndpoints || [];"
            cookies_js = "return window.cookieData || {};"
            headers_js = "return window.headerData || {};"
            
            page = self.crawler.crawler_manager.get_page()
            
            requests = await page.evaluate(requests_js)
            apis = await page.evaluate(api_js)
            cookies = await page.evaluate(cookies_js)
            headers = await page.evaluate(headers_js)
            
            return {
                "requests": requests,
                "apis": apis,
                "cookies": cookies,
                "headers": headers
            }
            
        except Exception as e:
            Logger.warning(f"⚠️ 提取网络数据失败: {e}")
            return {}
    
    def _analyze_network_data(self, network_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析网络数据"""
        Logger.info("🔬 分析网络数据...")
        
        requests = network_data.get("requests", [])
        apis = network_data.get("apis", [])
        cookies = network_data.get("cookies", {})
        headers = network_data.get("headers", {})
        
        analysis = {
            "summary": {
                "total_requests": len(requests),
                "api_requests": len(apis),
                "unique_domains": len(set([urlparse(req.get("url", "")).netloc for req in requests if req.get("url")])),
                "cookie_count": len(cookies.get("parsed", {}))
            },
            "api_endpoints": [],
            "job_related_apis": [],
            "authentication": {},
            "request_patterns": {},
            "cookies": cookies,
            "headers": headers
        }
        
        # 分析API端点
        for api in apis:
            url = api.get("url", "")
            method = api.get("method", "GET")
            
            endpoint_info = {
                "url": url,
                "method": method,
                "domain": urlparse(url).netloc,
                "path": urlparse(url).path,
                "query_params": parse_qs(urlparse(url).query),
                "headers": api.get("headers", {}),
                "response_status": api.get("status"),
                "has_json_response": "responseJson" in api
            }
            
            analysis["api_endpoints"].append(endpoint_info)
            
            # 检查是否为职位相关API
            if any(keyword in url.lower() for keyword in ['job', 'position', 'search', 'list', 'geek']):
                analysis["job_related_apis"].append(endpoint_info)
        
        # 分析认证信息
        auth_cookies = {}
        for key, value in cookies.get("parsed", {}).items():
            if any(auth_key in key.lower() for auth_key in ['token', 'session', 'auth', 'login', 'user']):
                auth_cookies[key] = value
        
        analysis["authentication"] = {
            "auth_cookies": auth_cookies,
            "auth_headers": {k: v for k, v in headers.items() if 'auth' in k.lower() or 'token' in k.lower()}
        }
        
        # 分析请求模式
        domains = {}
        methods = {}
        for req in requests:
            domain = urlparse(req.get("url", "")).netloc
            method = req.get("method", "GET")
            
            domains[domain] = domains.get(domain, 0) + 1
            methods[method] = methods.get(method, 0) + 1
        
        analysis["request_patterns"] = {
            "domains": domains,
            "methods": methods
        }
        
        Logger.success(f"✅ 网络分析完成: {analysis['summary']['total_requests']} 个请求, {analysis['summary']['api_requests']} 个API")
        
        return analysis
    
    async def _save_analysis_result(self, analysis: Dict[str, Any]):
        """保存分析结果"""
        try:
            # 保存完整分析结果
            with open("network_analysis_full.json", "w", encoding="utf-8") as f:
                json.dump(analysis, f, ensure_ascii=False, indent=2)
            
            # 保存API端点摘要
            api_summary = {
                "job_related_apis": analysis["job_related_apis"],
                "all_api_endpoints": analysis["api_endpoints"],
                "authentication": analysis["authentication"]
            }
            
            with open("api_endpoints.json", "w", encoding="utf-8") as f:
                json.dump(api_summary, f, ensure_ascii=False, indent=2)
            
            # 生成可读报告
            report = self._generate_readable_report(analysis)
            with open("network_analysis_report.txt", "w", encoding="utf-8") as f:
                f.write(report)
            
            Logger.success("💾 分析结果已保存:")
            Logger.info("   📄 network_analysis_full.json - 完整分析数据")
            Logger.info("   🔗 api_endpoints.json - API端点摘要")
            Logger.info("   📋 network_analysis_report.txt - 可读报告")
            
        except Exception as e:
            Logger.error(f"❌ 保存分析结果失败: {e}")
    
    def _generate_readable_report(self, analysis: Dict[str, Any]) -> str:
        """生成可读报告"""
        report = []
        report.append("=" * 60)
        report.append("BOSS直聘网络分析报告")
        report.append("=" * 60)
        
        # 摘要信息
        summary = analysis["summary"]
        report.append(f"\n📊 摘要信息:")
        report.append(f"   总请求数: {summary['total_requests']}")
        report.append(f"   API请求数: {summary['api_requests']}")
        report.append(f"   涉及域名: {summary['unique_domains']} 个")
        report.append(f"   Cookie数量: {summary['cookie_count']} 个")
        
        # 职位相关API
        job_apis = analysis["job_related_apis"]
        report.append(f"\n🎯 职位相关API ({len(job_apis)} 个):")
        for i, api in enumerate(job_apis[:10], 1):  # 只显示前10个
            report.append(f"   {i}. {api['method']} {api['url']}")
            if api.get('query_params'):
                report.append(f"      参数: {api['query_params']}")
        
        # 认证信息
        auth = analysis["authentication"]
        report.append(f"\n🔐 认证信息:")
        if auth["auth_cookies"]:
            report.append(f"   认证Cookie: {list(auth['auth_cookies'].keys())}")
        if auth["auth_headers"]:
            report.append(f"   认证Header: {list(auth['auth_headers'].keys())}")
        
        # 请求模式
        patterns = analysis["request_patterns"]
        report.append(f"\n📈 请求模式:")
        report.append(f"   主要域名: {dict(list(patterns['domains'].items())[:5])}")
        report.append(f"   请求方法: {patterns['methods']}")
        
        report.append("\n" + "=" * 60)
        
        return "\n".join(report)


async def analyze_boss_network():
    """分析BOSS直聘网络"""
    Logger.info("🚀 开始BOSS直聘网络分析...")
    
    urls_to_analyze = [
        "https://www.zhipin.com",
        "https://www.zhipin.com/web/geek/job?query=Python&city=101010100",
        "https://www.zhipin.com/c101010100/"
    ]
    
    async with NetworkAnalyzer() as analyzer:
        for url in urls_to_analyze:
            Logger.info(f"\n🔍 分析URL: {url}")
            result = await analyzer.deep_network_analysis(url)
            
            if result:
                Logger.success(f"✅ {url} 分析完成")
            else:
                Logger.warning(f"⚠️ {url} 分析失败")
            
            # 延时避免请求过快
            await asyncio.sleep(5)
    
    Logger.success("🎉 网络分析全部完成!")


if __name__ == "__main__":
    asyncio.run(analyze_boss_network())

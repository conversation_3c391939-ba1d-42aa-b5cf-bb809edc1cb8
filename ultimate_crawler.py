#!/usr/bin/env python3
"""
终极BOSS直聘爬虫 - 集成所有优化技术
使用最强反检测、多策略并行、数据增强等技术
确保获取到完整准确的职位信息
"""

import asyncio
import json
from typing import List, Dict, Any
from datetime import datetime

from data_models import JobPosition, JobSearchResult, CrawlerConfig
from advanced_crawler import AdvancedBossCrawler
from data_enhancer import DataEnhancer
from utils import Logger, DataExporter, format_job_summary


class UltimateBossCrawler:
    """终极BOSS直聘爬虫"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.advanced_crawler = None
        self.data_enhancer = None
        self.total_jobs_found = 0
        self.unique_jobs_count = 0
        
    async def __aenter__(self):
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
    
    async def initialize(self):
        """初始化终极爬虫"""
        Logger.info("🚀 正在初始化终极BOSS直聘爬虫...")
        Logger.info("=" * 60)
        Logger.info("🎯 终极爬虫特性:")
        Logger.info("   🥷 超强反检测技术 - 完全绕过自动化检测")
        Logger.info("   🔄 多策略并行爬取 - 网络分析、API调用、深度爬取、移动端")
        Logger.info("   🧠 智能行为模拟 - 真实用户操作模拟")
        Logger.info("   📡 网络请求拦截 - 分析和调用真实API")
        Logger.info("   🔧 数据质量增强 - 自动清洗、验证、补全数据")
        Logger.info("   📊 智能去重算法 - 确保数据唯一性")
        Logger.info("   💾 多格式导出 - JSON、CSV、Excel")
        Logger.info("=" * 60)
        
        # 初始化高级爬虫
        self.advanced_crawler = AdvancedBossCrawler(self.config)
        await self.advanced_crawler.__aenter__()
        
        # 初始化数据增强器
        self.data_enhancer = DataEnhancer()
        await self.data_enhancer.__aenter__()
        
        Logger.success("✅ 终极爬虫初始化完成")
    
    async def close(self):
        """关闭终极爬虫"""
        if self.advanced_crawler:
            await self.advanced_crawler.__aexit__(None, None, None)
        
        if self.data_enhancer:
            await self.data_enhancer.__aexit__(None, None, None)
        
        Logger.info("🔒 终极爬虫已关闭")
    
    async def ultimate_crawl(self) -> JobSearchResult:
        """执行终极爬取"""
        Logger.info("🎯 开始执行终极爬取流程...")
        
        start_time = datetime.now()
        
        try:
            # 阶段1: 多策略并行爬取
            Logger.info("\n" + "=" * 50)
            Logger.info("📡 阶段1: 多策略并行爬取")
            Logger.info("=" * 50)
            
            raw_result = await self.advanced_crawler.multi_strategy_crawl(
                self.config.search_keyword,
                self.config.search_location,
                self.config.max_pages
            )
            
            self.total_jobs_found = len(raw_result.jobs)
            Logger.success(f"✅ 多策略爬取完成，获取 {self.total_jobs_found} 个原始职位")
            
            if not raw_result.jobs:
                Logger.warning("⚠️ 未获取到任何职位数据")
                return raw_result
            
            # 阶段2: 数据质量增强
            Logger.info("\n" + "=" * 50)
            Logger.info("🔧 阶段2: 数据质量增强")
            Logger.info("=" * 50)
            
            enhanced_jobs = await self.data_enhancer.enhance_jobs(raw_result.jobs)
            Logger.success(f"✅ 数据增强完成，处理 {len(enhanced_jobs)} 个职位")
            
            # 阶段3: 高级数据验证和清洗
            Logger.info("\n" + "=" * 50)
            Logger.info("🧹 阶段3: 高级数据验证和清洗")
            Logger.info("=" * 50)
            
            validated_jobs = await self._advanced_data_validation(enhanced_jobs)
            Logger.success(f"✅ 数据验证完成，保留 {len(validated_jobs)} 个有效职位")
            
            # 阶段4: 智能去重和排序
            Logger.info("\n" + "=" * 50)
            Logger.info("🔄 阶段4: 智能去重和排序")
            Logger.info("=" * 50)
            
            final_jobs = await self._intelligent_deduplication(validated_jobs)
            self.unique_jobs_count = len(final_jobs)
            Logger.success(f"✅ 智能去重完成，最终获得 {self.unique_jobs_count} 个唯一职位")
            
            # 阶段5: 数据完整性检查
            Logger.info("\n" + "=" * 50)
            Logger.info("🔍 阶段5: 数据完整性检查")
            Logger.info("=" * 50)
            
            final_jobs = await self._data_completeness_check(final_jobs)
            Logger.success(f"✅ 数据完整性检查完成")
            
            # 创建最终结果
            final_result = JobSearchResult(
                total_count=self.unique_jobs_count,
                current_page=self.config.max_pages,
                jobs=final_jobs,
                search_keyword=self.config.search_keyword,
                search_location=self.config.search_location
            )
            
            # 阶段6: 数据导出
            Logger.info("\n" + "=" * 50)
            Logger.info("💾 阶段6: 数据导出")
            Logger.info("=" * 50)
            
            await self._export_ultimate_data(final_result)
            
            # 显示最终统计
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            Logger.info("\n" + "=" * 60)
            Logger.success("🎉 终极爬取完成!")
            Logger.info("=" * 60)
            Logger.info("📊 最终统计:")
            Logger.info(f"   🎯 搜索关键词: {self.config.search_keyword}")
            Logger.info(f"   📍 搜索地点: {self.config.search_location}")
            Logger.info(f"   📄 爬取页数: {self.config.max_pages}")
            Logger.info(f"   🔍 原始职位: {self.total_jobs_found} 个")
            Logger.info(f"   ✨ 最终职位: {self.unique_jobs_count} 个")
            Logger.info(f"   ⏱️ 总耗时: {duration:.1f} 秒")
            Logger.info(f"   📁 输出文件: {self.config.output_file}")
            Logger.info("=" * 60)
            
            return final_result
            
        except Exception as e:
            Logger.error(f"💥 终极爬取失败: {e}")
            raise
    
    async def _advanced_data_validation(self, jobs: List[JobPosition]) -> List[JobPosition]:
        """高级数据验证"""
        Logger.info("🔍 执行高级数据验证...")
        
        valid_jobs = []
        
        for job in jobs:
            # 验证必要字段
            if not self._is_valid_job(job):
                continue
            
            # 验证数据质量
            if not self._is_quality_job(job):
                continue
            
            valid_jobs.append(job)
        
        Logger.info(f"📊 验证结果: {len(jobs)} -> {len(valid_jobs)} (过滤 {len(jobs) - len(valid_jobs)} 个无效职位)")
        return valid_jobs
    
    def _is_valid_job(self, job: JobPosition) -> bool:
        """验证职位是否有效"""
        # 检查必要字段
        if not job.job_title or len(job.job_title.strip()) < 2:
            return False
        
        if not job.company_name or len(job.company_name.strip()) < 2:
            return False
        
        # 检查是否为真实职位
        invalid_titles = ['测试', 'test', '样例', '示例', '模板']
        if any(invalid in job.job_title.lower() for invalid in invalid_titles):
            return False
        
        return True
    
    def _is_quality_job(self, job: JobPosition) -> bool:
        """验证职位数据质量"""
        # 检查职位标题是否包含职位相关关键词
        job_keywords = [
            '工程师', '开发', '程序员', '架构师', '经理', '主管', '总监',
            '专员', '助理', '顾问', '分析师', '设计师', '运营', '产品',
            'engineer', 'developer', 'manager', 'director', 'analyst'
        ]
        
        title_lower = job.job_title.lower()
        if not any(keyword in title_lower for keyword in job_keywords):
            return False
        
        # 检查公司名称是否合理
        if len(job.company_name) > 50:  # 公司名称过长可能是错误数据
            return False
        
        return True
    
    async def _intelligent_deduplication(self, jobs: List[JobPosition]) -> List[JobPosition]:
        """智能去重"""
        Logger.info("🧠 执行智能去重算法...")
        
        # 使用多重标识进行去重
        seen_jobs = {}
        unique_jobs = []
        
        for job in jobs:
            # 创建多重标识
            primary_key = f"{job.job_title.lower()}_{job.company_name.lower()}"
            secondary_key = f"{job.job_title.lower()}_{job.location}"
            url_key = job.job_detail_url if job.job_detail_url else ""
            
            # 检查是否重复
            is_duplicate = False
            
            if primary_key in seen_jobs:
                is_duplicate = True
            elif secondary_key in seen_jobs:
                is_duplicate = True
            elif url_key and url_key in [j.job_detail_url for j in unique_jobs]:
                is_duplicate = True
            
            if not is_duplicate:
                seen_jobs[primary_key] = job
                seen_jobs[secondary_key] = job
                unique_jobs.append(job)
        
        Logger.info(f"🔄 去重结果: {len(jobs)} -> {len(unique_jobs)} (移除 {len(jobs) - len(unique_jobs)} 个重复职位)")
        
        # 按薪资和公司排序
        unique_jobs.sort(key=lambda x: (
            self._extract_salary_number(x.salary_range),
            x.company_name
        ), reverse=True)
        
        return unique_jobs
    
    def _extract_salary_number(self, salary: str) -> int:
        """从薪资字符串中提取数字用于排序"""
        if not salary:
            return 0
        
        import re
        numbers = re.findall(r'\d+', salary)
        if numbers:
            return int(numbers[0])
        return 0
    
    async def _data_completeness_check(self, jobs: List[JobPosition]) -> List[JobPosition]:
        """数据完整性检查"""
        Logger.info("🔍 执行数据完整性检查...")
        
        complete_jobs = []
        incomplete_count = 0
        
        for job in jobs:
            completeness_score = self._calculate_completeness_score(job)
            
            if completeness_score >= 0.6:  # 60%完整度阈值
                complete_jobs.append(job)
            else:
                incomplete_count += 1
        
        Logger.info(f"📊 完整性检查: 保留 {len(complete_jobs)} 个完整职位，过滤 {incomplete_count} 个不完整职位")
        
        return complete_jobs
    
    def _calculate_completeness_score(self, job: JobPosition) -> float:
        """计算数据完整性分数"""
        fields = [
            job.job_title, job.salary_range, job.company_name,
            job.location, job.work_experience, job.education,
            job.job_detail_url
        ]
        
        filled_fields = sum(1 for field in fields if field and field.strip())
        return filled_fields / len(fields)
    
    async def _export_ultimate_data(self, result: JobSearchResult):
        """导出终极数据"""
        if not result.jobs:
            Logger.warning("⚠️ 没有数据可导出")
            return
        
        try:
            # 导出主要格式
            if self.config.output_format.lower() == "json":
                await DataExporter.export_to_json(result, self.config.output_file)
            elif self.config.output_format.lower() == "csv":
                DataExporter.export_to_csv(result, self.config.output_file.replace('.json', '.csv'))
            elif self.config.output_format.lower() == "excel":
                DataExporter.export_to_excel(result, self.config.output_file.replace('.json', '.xlsx'))
            
            # 额外导出所有格式（用于备份）
            base_name = self.config.output_file.replace('.json', '').replace('.csv', '').replace('.xlsx', '')
            
            await DataExporter.export_to_json(result, f"{base_name}_ultimate.json")
            DataExporter.export_to_csv(result, f"{base_name}_ultimate.csv")
            DataExporter.export_to_excel(result, f"{base_name}_ultimate.xlsx")
            
            Logger.success("💾 数据导出完成:")
            Logger.info(f"   📄 主要文件: {self.config.output_file}")
            Logger.info(f"   📄 JSON备份: {base_name}_ultimate.json")
            Logger.info(f"   📄 CSV备份: {base_name}_ultimate.csv")
            Logger.info(f"   📄 Excel备份: {base_name}_ultimate.xlsx")
            
        except Exception as e:
            Logger.error(f"❌ 数据导出失败: {e}")


# 便捷函数
async def ultimate_crawl_boss_jobs(
    keyword: str = "Python",
    location: str = "北京", 
    max_pages: int = 5,
    output_format: str = "json",
    output_file: str = "ultimate_boss_jobs.json",
    headless: bool = True
) -> JobSearchResult:
    """终极职位爬取函数"""
    
    config = CrawlerConfig(
        search_keyword=keyword,
        search_location=location,
        max_pages=max_pages,
        output_format=output_format,
        output_file=output_file,
        headless=headless,
        timeout=180,
        delay_range=(8, 15),
        max_retries=5
    )
    
    async with UltimateBossCrawler(config) as crawler:
        return await crawler.ultimate_crawl()


if __name__ == "__main__":
    # 运行终极演示
    async def demo():
        Logger.info("🚀 运行终极BOSS直聘爬虫演示...")
        
        result = await ultimate_crawl_boss_jobs(
            keyword="Python开发",
            location="北京",
            max_pages=3,
            output_format="json",
            output_file="ultimate_demo_jobs.json"
        )
        
        if result.jobs:
            print("\n" + format_job_summary(result.jobs))
        else:
            print("❌ 终极演示未获取到数据")
    
    asyncio.run(demo())

#!/usr/bin/env python3
"""
高级BOSS直聘爬虫 - 使用最强反检测技术
深度优化性能和准确性，突破所有反爬虫限制
"""

import asyncio
import json
import random
import time
import re
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, urlparse, parse_qs
import base64

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy

from data_models import JobPosition, JobSearchResult, CrawlerConfig
from utils import RequestHelper, DataCleaner, Logger, DataExporter


class AdvancedAntiDetection:
    """高级反检测技术"""
    
    @staticmethod
    def get_stealth_config() -> Dict[str, Any]:
        """获取隐身配置"""
        return {
            "extra_args": [
                "--disable-blink-features=AutomationControlled",
                "--disable-dev-shm-usage",
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-gpu",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-features=TranslateUI,VizDisplayCompositor",
                "--disable-ipc-flooding-protection",
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor",
                "--disable-extensions",
                "--disable-plugins",
                "--disable-images",
                "--disable-javascript-harmony-shipping",
                "--disable-client-side-phishing-detection",
                "--disable-sync",
                "--disable-default-apps",
                "--hide-scrollbars",
                "--mute-audio",
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-background-networking",
                "--disable-background-timer-throttling",
                "--disable-renderer-backgrounding",
                "--disable-backgrounding-occluded-windows",
                "--disable-features=TranslateUI",
                "--disable-component-extensions-with-background-pages",
                "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ]
        }
    
    @staticmethod
    def get_stealth_js() -> str:
        """获取隐身JavaScript代码"""
        return """
        // 完全移除webdriver痕迹
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        // 伪造chrome对象
        window.chrome = {
            runtime: {},
            loadTimes: function() {},
            csi: function() {},
            app: {}
        };
        
        // 伪造plugins
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
        
        // 伪造languages
        Object.defineProperty(navigator, 'languages', {
            get: () => ['zh-CN', 'zh', 'en'],
        });
        
        // 移除自动化标识
        delete navigator.__proto__.webdriver;
        
        // 伪造权限API
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
        );
        
        // 伪造硬件信息
        Object.defineProperty(navigator, 'hardwareConcurrency', {
            get: () => 8,
        });
        
        Object.defineProperty(navigator, 'deviceMemory', {
            get: () => 8,
        });
        
        // 伪造屏幕信息
        Object.defineProperty(screen, 'width', {
            get: () => 1920,
        });
        
        Object.defineProperty(screen, 'height', {
            get: () => 1080,
        });
        
        // 移除Playwright痕迹
        delete window.playwright;
        delete window.__playwright;
        delete window._playwright;
        
        // 伪造Date对象
        const originalDate = Date;
        Date = class extends originalDate {
            constructor(...args) {
                if (args.length === 0) {
                    super();
                } else {
                    super(...args);
                }
            }
            
            static now() {
                return originalDate.now();
            }
        };
        
        // 等待页面稳定
        await new Promise(resolve => setTimeout(resolve, 2000));
        """


class BehaviorSimulator:
    """真实用户行为模拟器"""
    
    @staticmethod
    def get_human_behavior_js() -> str:
        """获取人类行为模拟JavaScript"""
        return """
        // 模拟真实用户行为
        const simulateHumanBehavior = async () => {
            // 随机鼠标移动
            const moveCount = Math.floor(Math.random() * 5) + 3;
            for (let i = 0; i < moveCount; i++) {
                const x = Math.random() * window.innerWidth;
                const y = Math.random() * window.innerHeight;
                
                const event = new MouseEvent('mousemove', {
                    clientX: x,
                    clientY: y,
                    bubbles: true
                });
                document.dispatchEvent(event);
                
                await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 200));
            }
            
            // 模拟滚动行为
            const scrollSteps = Math.floor(Math.random() * 8) + 5;
            for (let i = 0; i < scrollSteps; i++) {
                const scrollY = (window.innerHeight / scrollSteps) * i;
                window.scrollTo({
                    top: scrollY,
                    behavior: 'smooth'
                });
                
                await new Promise(resolve => setTimeout(resolve, Math.random() * 800 + 400));
            }
            
            // 模拟页面交互
            const interactiveElements = document.querySelectorAll('button, a, input');
            if (interactiveElements.length > 0) {
                const randomElement = interactiveElements[Math.floor(Math.random() * interactiveElements.length)];
                
                // 模拟hover
                const hoverEvent = new MouseEvent('mouseenter', {
                    bubbles: true,
                    cancelable: true
                });
                randomElement.dispatchEvent(hoverEvent);
                
                await new Promise(resolve => setTimeout(resolve, Math.random() * 300 + 100));
            }
            
            // 最终滚动到页面底部
            window.scrollTo({
                top: document.body.scrollHeight,
                behavior: 'smooth'
            });
            
            await new Promise(resolve => setTimeout(resolve, 2000));
        };
        
        await simulateHumanBehavior();
        """


class NetworkInterceptor:
    """网络请求拦截器"""
    
    @staticmethod
    def get_network_intercept_js() -> str:
        """获取网络拦截JavaScript"""
        return """
        // 拦截所有网络请求
        const originalFetch = window.fetch;
        const originalXHR = window.XMLHttpRequest;
        
        window.interceptedRequests = [];
        
        // 拦截fetch请求
        window.fetch = function(...args) {
            const url = args[0];
            const options = args[1] || {};
            
            window.interceptedRequests.push({
                type: 'fetch',
                url: url,
                method: options.method || 'GET',
                headers: options.headers || {},
                body: options.body,
                timestamp: Date.now()
            });
            
            return originalFetch.apply(this, args);
        };
        
        // 拦截XMLHttpRequest
        const XHROpen = originalXHR.prototype.open;
        const XHRSend = originalXHR.prototype.send;
        
        originalXHR.prototype.open = function(method, url, ...args) {
            this._method = method;
            this._url = url;
            return XHROpen.apply(this, [method, url, ...args]);
        };
        
        originalXHR.prototype.send = function(body) {
            window.interceptedRequests.push({
                type: 'xhr',
                url: this._url,
                method: this._method,
                body: body,
                timestamp: Date.now()
            });
            
            return XHRSend.apply(this, [body]);
        };
        
        // 等待网络请求
        await new Promise(resolve => setTimeout(resolve, 5000));
        """


class AdvancedBossCrawler:
    """高级BOSS直聘爬虫"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.browser_config = None
        self.crawler = None
        self.intercepted_requests = []
        self.api_endpoints = []
        
    async def __aenter__(self):
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
    
    async def initialize(self):
        """初始化高级爬虫"""
        Logger.info("🚀 正在初始化高级BOSS直聘爬虫...")
        
        # 获取隐身配置
        stealth_config = AdvancedAntiDetection.get_stealth_config()
        
        # 配置浏览器
        self.browser_config = BrowserConfig(
            headless=self.config.headless,
            viewport_width=1920,
            viewport_height=1080,
            java_script_enabled=True,
            text_mode=False,
            proxy=self.config.proxy_url if self.config.use_proxy else None,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            headers={
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "DNT": "1",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Cache-Control": "max-age=0"
            },
            **stealth_config
        )
        
        # 创建爬虫实例
        self.crawler = AsyncWebCrawler(config=self.browser_config)
        await self.crawler.__aenter__()
        
        Logger.success("✅ 高级爬虫初始化完成")
    
    async def close(self):
        """关闭爬虫"""
        if self.crawler:
            await self.crawler.__aexit__(None, None, None)
            Logger.info("🔒 高级爬虫已关闭")
    
    async def analyze_network_requests(self, url: str) -> List[Dict[str, Any]]:
        """分析网络请求"""
        Logger.info(f"🔍 正在分析网络请求: {url}")
        
        # 组合所有JavaScript代码
        combined_js = "\n".join([
            AdvancedAntiDetection.get_stealth_js(),
            NetworkInterceptor.get_network_intercept_js(),
            BehaviorSimulator.get_human_behavior_js()
        ])
        
        run_config = CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            page_timeout=120000,
            delay_before_return_html=10.0,
            js_code=[combined_js]
        )
        
        try:
            result = await self.crawler.arun(url=url, config=run_config)
            
            if result.success:
                # 获取拦截的请求
                requests_js = """
                return window.interceptedRequests || [];
                """
                
                # 执行JavaScript获取拦截的请求
                try:
                    intercepted = await self.crawler.crawler_manager.get_page().evaluate(requests_js)
                    self.intercepted_requests = intercepted
                    Logger.success(f"🎯 拦截到 {len(intercepted)} 个网络请求")
                    
                    # 分析API端点
                    for req in intercepted:
                        if 'api' in req.get('url', '').lower() or 'job' in req.get('url', '').lower():
                            self.api_endpoints.append(req)
                    
                    Logger.info(f"🔗 发现 {len(self.api_endpoints)} 个潜在API端点")
                    return intercepted
                    
                except Exception as e:
                    Logger.warning(f"⚠️ 获取拦截请求失败: {e}")
                    return []
            else:
                Logger.error(f"❌ 网络分析失败: {result.error_message}")
                return []
                
        except Exception as e:
            Logger.error(f"💥 网络分析异常: {e}")
            return []
    
    async def direct_api_crawl(self) -> List[JobPosition]:
        """直接调用API爬取"""
        Logger.info("🎯 尝试直接API调用...")
        
        jobs = []
        
        # 分析拦截的API请求
        for api_req in self.api_endpoints:
            try:
                url = api_req.get('url', '')
                method = api_req.get('method', 'GET')
                
                Logger.info(f"📡 尝试API: {url}")
                
                # 构造API请求
                if 'job' in url.lower():
                    # 这可能是职位相关的API
                    api_result = await self._call_api(url, method)
                    if api_result:
                        api_jobs = self._parse_api_response(api_result)
                        jobs.extend(api_jobs)
                        
            except Exception as e:
                Logger.warning(f"⚠️ API调用失败: {e}")
                continue
        
        Logger.success(f"🎉 API爬取完成，获取 {len(jobs)} 个职位")
        return jobs
    
    async def _call_api(self, url: str, method: str = 'GET') -> Optional[Dict[str, Any]]:
        """调用API"""
        try:
            # 使用爬虫的session调用API
            headers = {
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Referer": "https://www.zhipin.com/",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            }
            
            # 这里需要实现实际的HTTP请求
            # 由于crawl4ai的限制，我们使用页面执行JavaScript来调用API
            api_js = f"""
            try {{
                const response = await fetch('{url}', {{
                    method: '{method}',
                    headers: {{
                        'Accept': 'application/json, text/plain, */*',
                        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                        'Referer': 'https://www.zhipin.com/'
                    }}
                }});
                
                if (response.ok) {{
                    const data = await response.json();
                    return data;
                }} else {{
                    return null;
                }}
            }} catch (error) {{
                return null;
            }}
            """
            
            result = await self.crawler.crawler_manager.get_page().evaluate(api_js)
            return result
            
        except Exception as e:
            Logger.warning(f"⚠️ API调用异常: {e}")
            return None
    
    def _parse_api_response(self, data: Dict[str, Any]) -> List[JobPosition]:
        """解析API响应"""
        jobs = []
        
        try:
            # 根据BOSS直聘的API结构解析数据
            # 这里需要根据实际的API响应格式进行调整
            if isinstance(data, dict):
                job_list = data.get('zpData', {}).get('jobList', [])
                if not job_list:
                    job_list = data.get('jobList', [])
                if not job_list:
                    job_list = data.get('data', [])
                
                for job_data in job_list:
                    if isinstance(job_data, dict):
                        job = self._create_job_from_api(job_data)
                        if job:
                            jobs.append(job)
                            
        except Exception as e:
            Logger.warning(f"⚠️ API响应解析失败: {e}")
        
        return jobs
    
    def _create_job_from_api(self, job_data: Dict[str, Any]) -> Optional[JobPosition]:
        """从API数据创建职位对象"""
        try:
            # 根据BOSS直聘的数据结构创建职位对象
            job = JobPosition(
                job_title=job_data.get('jobName', ''),
                job_requirements=f"{job_data.get('jobExperience', '')} {job_data.get('jobDegree', '')}",
                salary_range=job_data.get('salaryDesc', ''),
                company_name=job_data.get('brandName', ''),
                company_scale=job_data.get('brandScaleName', ''),
                company_industry=job_data.get('brandIndustry', ''),
                job_detail_url=f"https://www.zhipin.com/job_detail/{job_data.get('encryptJobId', '')}.html",
                location=job_data.get('cityName', ''),
                work_experience=job_data.get('jobExperience', ''),
                education=job_data.get('jobDegree', ''),
                job_description=job_data.get('jobLabels', []),
                welfare_benefits=job_data.get('welfareList', []),
                hr_info=job_data.get('bossName', ''),
                publish_time=job_data.get('lastModifyTime', '')
            )
            return job
            
        except Exception as e:
            Logger.warning(f"⚠️ 创建职位对象失败: {e}")
            return None

    async def multi_strategy_crawl(self, keyword: str, location: str, max_pages: int = 5) -> JobSearchResult:
        """多策略并行爬取"""
        Logger.info(f"🎯 开始多策略爬取: 关键词='{keyword}', 地点='{location}', 页数={max_pages}")

        all_jobs = []

        # 策略1: 分析网络请求
        Logger.info("📡 策略1: 网络请求分析")
        search_url = f"https://www.zhipin.com/web/geek/job?query={keyword}&city=101010100"
        await self.analyze_network_requests(search_url)

        # 策略2: 直接API调用
        Logger.info("🎯 策略2: 直接API调用")
        api_jobs = await self.direct_api_crawl()
        all_jobs.extend(api_jobs)

        # 策略3: 深度页面爬取
        Logger.info("🕷️ 策略3: 深度页面爬取")
        page_jobs = await self.deep_page_crawl(keyword, location, max_pages)
        all_jobs.extend(page_jobs)

        # 策略4: 移动端爬取
        Logger.info("📱 策略4: 移动端爬取")
        mobile_jobs = await self.mobile_crawl(keyword, location)
        all_jobs.extend(mobile_jobs)

        # 去重和清洗
        unique_jobs = self._deduplicate_jobs(all_jobs)

        result = JobSearchResult(
            total_count=len(unique_jobs),
            current_page=max_pages,
            jobs=unique_jobs,
            search_keyword=keyword,
            search_location=location
        )

        Logger.success(f"🎉 多策略爬取完成，共获取 {len(unique_jobs)} 个唯一职位")
        return result

    async def deep_page_crawl(self, keyword: str, location: str, max_pages: int) -> List[JobPosition]:
        """深度页面爬取"""
        jobs = []

        # 尝试多种URL格式
        url_patterns = [
            f"https://www.zhipin.com/web/geek/job?query={keyword}&city=101010100&page={{page}}",
            f"https://www.zhipin.com/c101010100/?query={keyword}&page={{page}}",
            f"https://www.zhipin.com/job_detail/?ka=search_list&query={keyword}&page={{page}}"
        ]

        for pattern in url_patterns:
            Logger.info(f"🔄 尝试URL模式: {pattern}")

            for page in range(1, max_pages + 1):
                url = pattern.format(page=page)
                page_jobs = await self._crawl_single_page_advanced(url, page)
                jobs.extend(page_jobs)

                # 随机延时
                await asyncio.sleep(random.uniform(3, 8))

        return jobs

    async def _crawl_single_page_advanced(self, url: str, page: int) -> List[JobPosition]:
        """高级单页爬取"""
        jobs = []

        # 超强反检测JavaScript
        super_stealth_js = f"""
        {AdvancedAntiDetection.get_stealth_js()}

        // 额外的反检测措施
        Object.defineProperty(navigator, 'platform', {{
            get: () => 'Win32',
        }});

        Object.defineProperty(navigator, 'vendor', {{
            get: () => 'Google Inc.',
        }});

        // 伪造电池API
        navigator.getBattery = () => Promise.resolve({{
            charging: true,
            chargingTime: 0,
            dischargingTime: Infinity,
            level: 0.8
        }});

        // 伪造地理位置API
        navigator.geolocation.getCurrentPosition = (success, error) => {{
            success({{
                coords: {{
                    latitude: 39.9042,
                    longitude: 116.4074,
                    accuracy: 20
                }}
            }});
        }};

        {BehaviorSimulator.get_human_behavior_js()}

        // 等待页面完全加载
        await new Promise(resolve => {{
            const checkLoaded = () => {{
                if (document.readyState === 'complete') {{
                    setTimeout(resolve, 3000);
                }} else {{
                    setTimeout(checkLoaded, 100);
                }}
            }};
            checkLoaded();
        }});

        // 尝试触发懒加载
        const triggerLazyLoad = async () => {{
            const scrollHeight = document.body.scrollHeight;
            const viewportHeight = window.innerHeight;
            const scrollSteps = Math.ceil(scrollHeight / viewportHeight);

            for (let i = 0; i <= scrollSteps; i++) {{
                window.scrollTo(0, i * viewportHeight);
                await new Promise(resolve => setTimeout(resolve, 1000));
            }}

            // 尝试点击"加载更多"按钮
            const loadMoreButtons = document.querySelectorAll('[class*="load"], [class*="more"], [class*="next"]');
            for (const btn of loadMoreButtons) {{
                if (btn.offsetParent !== null) {{
                    btn.click();
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }}
            }}
        }};

        await triggerLazyLoad();
        """

        # 多种CSS选择器策略
        selector_strategies = [
            {
                "name": "BOSS直聘标准选择器",
                "baseSelector": ".job-list li, [class*='job-card'], [class*='job-item'], .job-primary",
                "fields": [
                    {"name": "job_title", "selector": ".job-name, .job-title, [class*='job-name'], [class*='title']", "type": "text"},
                    {"name": "salary_range", "selector": ".salary, .red, [class*='salary'], [class*='money']", "type": "text"},
                    {"name": "company_name", "selector": ".company-name, [class*='company']", "type": "text"},
                    {"name": "job_detail_url", "selector": "a", "type": "attribute", "attribute": "href"},
                    {"name": "location", "selector": ".job-area, [class*='area'], [class*='location']", "type": "text"}
                ]
            },
            {
                "name": "通用职位选择器",
                "baseSelector": "li, div, article",
                "fields": [
                    {"name": "job_title", "selector": "h1, h2, h3, h4, .title, [class*='title']", "type": "text"},
                    {"name": "salary_range", "selector": "[class*='salary'], [class*='money'], .price", "type": "text"},
                    {"name": "company_name", "selector": "[class*='company'], [class*='brand']", "type": "text"}
                ]
            }
        ]

        for strategy in selector_strategies:
            try:
                extraction_strategy = JsonCssExtractionStrategy(strategy, verbose=True)

                run_config = CrawlerRunConfig(
                    cache_mode=CacheMode.BYPASS,
                    extraction_strategy=extraction_strategy,
                    page_timeout=180000,  # 3分钟超时
                    delay_before_return_html=15.0,  # 等待15秒
                    js_code=[super_stealth_js]
                )

                result = await self.crawler.arun(url=url, config=run_config)

                if result.success and result.extracted_content:
                    raw_jobs = json.loads(result.extracted_content)

                    for job_data in raw_jobs:
                        if self._is_valid_job_data(job_data):
                            cleaned_data = DataCleaner.clean_job_data(job_data)
                            job = self._create_job_from_scraped_data(cleaned_data)
                            if job:
                                jobs.append(job)

                    if jobs:
                        Logger.success(f"✅ 策略 '{strategy['name']}' 成功，获取 {len(jobs)} 个职位")
                        break
                    else:
                        Logger.warning(f"⚠️ 策略 '{strategy['name']}' 未获取到有效数据")
                else:
                    Logger.warning(f"⚠️ 策略 '{strategy['name']}' 执行失败")

            except Exception as e:
                Logger.warning(f"⚠️ 策略 '{strategy['name']}' 异常: {e}")
                continue

        return jobs

    async def mobile_crawl(self, keyword: str, location: str) -> List[JobPosition]:
        """移动端爬取"""
        Logger.info("📱 尝试移动端爬取...")

        # 配置移动端浏览器
        mobile_config = BrowserConfig(
            headless=True,
            viewport_width=375,
            viewport_height=667,
            java_script_enabled=True,
            user_agent="Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1",
            extra_args=["--disable-blink-features=AutomationControlled"]
        )

        mobile_crawler = AsyncWebCrawler(config=mobile_config)
        jobs = []

        try:
            await mobile_crawler.__aenter__()

            mobile_url = f"https://m.zhipin.com/search/job.html?query={keyword}&city=101010100"

            mobile_js = f"""
            {AdvancedAntiDetection.get_stealth_js()}

            // 移动端特定行为
            const simulateMobileTouch = async () => {{
                const touchEvent = new TouchEvent('touchstart', {{
                    bubbles: true,
                    cancelable: true,
                    touches: [{{
                        clientX: Math.random() * window.innerWidth,
                        clientY: Math.random() * window.innerHeight
                    }}]
                }});
                document.dispatchEvent(touchEvent);

                await new Promise(resolve => setTimeout(resolve, 1000));
            }};

            await simulateMobileTouch();

            // 滚动加载
            for (let i = 0; i < 5; i++) {{
                window.scrollTo(0, document.body.scrollHeight);
                await new Promise(resolve => setTimeout(resolve, 2000));
            }}
            """

            run_config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                page_timeout=120000,
                delay_before_return_html=10.0,
                js_code=[mobile_js]
            )

            result = await mobile_crawler.arun(url=mobile_url, config=run_config)

            if result.success:
                Logger.success("✅ 移动端页面访问成功")
                # 这里可以添加移动端特定的数据解析逻辑
            else:
                Logger.warning("⚠️ 移动端页面访问失败")

        except Exception as e:
            Logger.warning(f"⚠️ 移动端爬取异常: {e}")
        finally:
            await mobile_crawler.__aexit__(None, None, None)

        return jobs

    def _is_valid_job_data(self, job_data: Dict[str, Any]) -> bool:
        """验证职位数据有效性"""
        if not isinstance(job_data, dict):
            return False

        # 检查必要字段
        required_fields = ['job_title', 'company_name']
        for field in required_fields:
            value = job_data.get(field, '')
            if not value or len(str(value).strip()) < 2:
                return False

        # 检查是否包含职位相关关键词
        title = str(job_data.get('job_title', '')).lower()
        if not any(keyword in title for keyword in ['工程师', '开发', 'engineer', 'developer', '经理', 'manager', '专员', '助理']):
            return False

        return True

    def _create_job_from_scraped_data(self, job_data: Dict[str, Any]) -> Optional[JobPosition]:
        """从爬取数据创建职位对象"""
        try:
            # 处理详情页URL
            detail_url = job_data.get("job_detail_url", "")
            if detail_url and detail_url.startswith("/"):
                detail_url = urljoin("https://www.zhipin.com", detail_url)

            job = JobPosition(
                job_title=job_data.get("job_title", ""),
                job_requirements=f"{job_data.get('work_experience', '')} {job_data.get('education', '')}".strip(),
                salary_range=job_data.get("salary_range", ""),
                company_name=job_data.get("company_name", ""),
                company_scale=job_data.get("company_scale", ""),
                company_industry=job_data.get("company_industry", ""),
                job_detail_url=detail_url,
                location=job_data.get("location", ""),
                work_experience=job_data.get("work_experience", ""),
                education=job_data.get("education", ""),
                job_description=job_data.get("job_description", ""),
                welfare_benefits=job_data.get("welfare_benefits", ""),
                hr_info=job_data.get("hr_info", ""),
                publish_time=job_data.get("publish_time", "")
            )
            return job

        except Exception as e:
            Logger.warning(f"⚠️ 创建职位对象失败: {e}")
            return None

    def _deduplicate_jobs(self, jobs: List[JobPosition]) -> List[JobPosition]:
        """去重职位"""
        seen = set()
        unique_jobs = []

        for job in jobs:
            # 使用职位标题和公司名称作为去重标识
            key = f"{job.job_title}_{job.company_name}".lower()
            if key not in seen:
                seen.add(key)
                unique_jobs.append(job)

        Logger.info(f"🔄 去重完成: {len(jobs)} -> {len(unique_jobs)}")
        return unique_jobs

    async def run_advanced_crawler(self) -> JobSearchResult:
        """运行高级爬虫"""
        try:
            result = await self.multi_strategy_crawl(
                self.config.search_keyword,
                self.config.search_location,
                self.config.max_pages
            )

            # 导出数据
            if result.jobs:
                await self._export_data(result)

            return result

        except Exception as e:
            Logger.error(f"💥 高级爬虫运行失败: {e}")
            raise

    async def _export_data(self, result: JobSearchResult):
        """导出数据"""
        if not result.jobs:
            Logger.warning("⚠️ 没有数据可导出")
            return

        try:
            if self.config.output_format.lower() == "json":
                await DataExporter.export_to_json(result, self.config.output_file)
            elif self.config.output_format.lower() == "csv":
                DataExporter.export_to_csv(result, self.config.output_file.replace('.json', '.csv'))
            elif self.config.output_format.lower() == "excel":
                DataExporter.export_to_excel(result, self.config.output_file.replace('.json', '.xlsx'))
            else:
                await DataExporter.export_to_json(result, self.config.output_file)

        except Exception as e:
            Logger.error(f"❌ 数据导出失败: {e}")


# 便捷函数
async def advanced_crawl_boss_jobs(
    keyword: str = "Python",
    location: str = "北京",
    max_pages: int = 5,
    output_format: str = "json",
    output_file: str = "advanced_boss_jobs.json",
    headless: bool = True
) -> JobSearchResult:
    """高级职位爬取函数"""

    config = CrawlerConfig(
        search_keyword=keyword,
        search_location=location,
        max_pages=max_pages,
        output_format=output_format,
        output_file=output_file,
        headless=headless,
        timeout=180,  # 增加超时时间
        delay_range=(5, 12)  # 增加延时范围
    )

    async with AdvancedBossCrawler(config) as crawler:
        return await crawler.run_advanced_crawler()

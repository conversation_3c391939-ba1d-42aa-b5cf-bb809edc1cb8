# BOSS直聘职位爬虫

基于 crawl4ai 框架的 BOSS直聘职位信息爬虫工具。

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- 操作系统: Windows/macOS/Linux

### 2. 安装依赖

```bash
# 克隆或下载项目
cd Position_Crawler

# 创建虚拟环境（推荐）
python3 -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 安装 Playwright 浏览器
playwright install
```

### 3. 运行演示

```bash
# 首先运行演示版本，了解完整功能
python demo_crawler.py
```

这将展示：
- ✅ 完整的数据提取流程
- ✅ 多格式数据导出 (JSON, CSV, Excel)
- ✅ 结构化数据模型
- ✅ 错误处理和日志系统

### 4. 基本使用（需要适配）

⚠️ **重要提示**: 由于BOSS直聘的反爬虫机制，以下命令需要先按照 `BOSS_ADAPTATION_GUIDE.md` 进行适配。

```bash
# 使用默认参数（爬取北京Python职位）
python main.py

# 指定搜索关键词和地点
python main.py --keyword "Java开发" --location "上海"

# 爬取更多页面并导出Excel
python main.py --keyword "前端开发" --pages 10 --format excel

# 显示浏览器界面（调试模式）
python main.py --headless false --verbose
```

### 4. 快速演示

```bash
# 运行演示模式（使用示例数据展示完整功能）
python demo_crawler.py

# 或者运行基础测试（测试网站访问能力）
python test_basic.py
```

**注意**: 由于BOSS直聘有较强的反爬虫机制，建议先运行演示版本熟悉功能，然后参考 `BOSS_ADAPTATION_GUIDE.md` 进行实际网站适配。

## 📖 详细使用说明

### 命令行参数

| 参数 | 简写 | 类型 | 默认值 | 说明 |
|------|------|------|--------|------|
| `--keyword` | `-k` | str | "Python" | 搜索关键词 |
| `--location` | `-l` | str | "北京" | 搜索地点 |
| `--pages` | `-p` | int | 3 | 爬取页数 |
| `--format` | `-f` | str | "json" | 输出格式 (json/csv/excel) |
| `--output` | `-o` | str | 自动生成 | 输出文件名 |
| `--headless` | - | str | "true" | 无头模式 (true/false) |
| `--delay` | - | float | 3.0 | 请求间隔时间(秒) |
| `--timeout` | - | int | 30 | 页面超时时间(秒) |
| `--proxy` | - | str | - | 代理服务器地址 |
| `--detail` | - | bool | False | 是否爬取详情页 |
| `--verbose` | `-v` | bool | False | 显示详细日志 |

### 使用示例

```bash
# 基础使用
python main.py --keyword "Python开发" --location "深圳" --pages 5

# 使用代理
python main.py --proxy "http://127.0.0.1:8080"

# 爬取详情页（耗时较长）
python main.py --detail --pages 2

# 导出Excel格式
python main.py --format excel --output "jobs_report.xlsx"

# 调试模式
python main.py --headless false --verbose --pages 1
```

### 编程接口

```python
import asyncio
from boss_crawler import crawl_boss_jobs

async def main():
    # 使用便捷函数
    result = await crawl_boss_jobs(
        keyword="数据分析师",
        location="杭州",
        max_pages=3,
        output_format="csv"
    )
    
    print(f"获取到 {len(result.jobs)} 个职位")
    for job in result.jobs[:5]:
        print(f"{job.job_title} - {job.company_name} - {job.salary_range}")

if __name__ == "__main__":
    asyncio.run(main())
```

## ⚙️ 配置说明

### 支持的城市

目前支持以下主要城市：
- 北京、上海、广州、深圳
- 杭州、南京、武汉、成都
- 西安、重庆

可在 `config.py` 中添加更多城市代码。

### 反爬虫策略

1. **随机延时**: 页面间 2-5 秒随机延时
2. **User-Agent 轮换**: 随机使用不同浏览器标识
3. **请求头伪装**: 模拟真实浏览器请求
4. **代理支持**: 支持 HTTP/SOCKS 代理
5. **重试机制**: 失败自动重试最多 3 次

### 数据输出格式

#### JSON 格式
```json
{
  "meta": {
    "total_count": 50,
    "search_keyword": "Python",
    "export_time": "2024-01-01T12:00:00"
  },
  "jobs": [
    {
      "job_title": "Python开发工程师",
      "salary_range": "15K-25K",
      "company_name": "某科技公司",
      "job_detail_url": "https://www.zhipin.com/job_detail/xxx"
    }
  ]
}
```

#### CSV 格式
包含所有字段的表格数据，适合 Excel 打开分析。

#### Excel 格式
包含两个工作表：
- **职位信息**: 详细的职位数据
- **元数据**: 爬取统计信息

## 🔧 高级配置

### 自定义 CSS 选择器

在 `config.py` 中修改 `CSS_SELECTORS` 配置：

```python
CSS_SELECTORS = {
    "job_list": {
        "baseSelector": ".job-card-wrapper",
        "fields": [
            {
                "name": "job_title",
                "selector": ".job-name",
                "type": "text"
            }
            # 添加更多字段...
        ]
    }
}
```

### 自定义延时策略

```python
DELAY_CONFIG = {
    "min_delay": 2,      # 最小延时
    "max_delay": 5,      # 最大延时
    "page_delay": 3,     # 页面间延时
    "detail_delay": 1,   # 详情页延时
}
```

## 🚨 注意事项

1. **合规使用**: 请遵守网站的使用条款，合理控制爬取频率
2. **数据用途**: 爬取的数据仅供个人学习和研究使用
3. **网络环境**: 建议在稳定的网络环境下运行
4. **资源消耗**: 爬取详情页会显著增加时间和资源消耗
5. **反爬虫**: 如遇到反爬虫限制，请适当增加延时或使用代理

## 🐛 故障排除

### 常见问题

1. **安装失败**
   ```bash
   # 升级 pip
   pip install --upgrade pip
   
   # 使用国内镜像
   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
   ```

2. **浏览器启动失败**
   ```bash
   # 重新安装 Playwright
   playwright install --force
   ```

3. **爬取失败**
   - 检查网络连接
   - 尝试使用代理
   - 增加延时时间
   - 开启详细日志查看错误信息

4. **数据不完整**
   - 检查 CSS 选择器是否正确
   - 网站结构可能已更新，需要调整选择器

### 调试模式

```bash
# 开启详细日志和浏览器界面
python main.py --verbose --headless false --pages 1
```

## 📄 许可证

本项目仅供学习和研究使用，请勿用于商业用途。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

---

**免责声明**: 本工具仅供学习和研究使用，使用者需自行承担使用风险，并遵守相关法律法规和网站使用条款。

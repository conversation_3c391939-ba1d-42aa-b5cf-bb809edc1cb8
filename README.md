# BOSS直聘职位爬虫

基于 crawl4ai 框架的 BOSS直聘职位信息爬虫工具。

## 安装

```bash
# 安装依赖
pip install -r requirements.txt

# 安装浏览器
playwright install
```

## 使用

```bash
# 基本使用
python main.py

# 指定参数
python main.py --keyword "Python" --location "北京" --pages 5 --format json

# 查看所有参数
python main.py --help
```

## 参数说明

- `--keyword` / `-k`: 搜索关键词（默认: Python）
- `--location` / `-l`: 搜索地点（默认: 北京）
- `--pages` / `-p`: 爬取页数（默认: 3）
- `--format` / `-f`: 输出格式 json/csv/excel（默认: json）
- `--output` / `-o`: 输出文件名
- `--headless`: 无头模式 true/false（默认: true）
- `--proxy`: 代理服务器地址
- `--verbose` / `-v`: 详细日志

## 输出

爬取的数据包含以下字段：
- 岗位名称
- 薪资范围
- 公司名称
- 工作地点
- 工作经验要求
- 学历要求
- 职位详情链接
- 职位描述
- 公司规模
- 公司行业

## 注意事项

1. 遵守网站使用条款
2. 控制爬取频率
3. 仅供学习研究使用
4. 网站结构变化时需要更新CSS选择器配置

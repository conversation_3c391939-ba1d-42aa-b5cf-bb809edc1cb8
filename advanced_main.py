#!/usr/bin/env python3
"""
高级BOSS直聘爬虫 - 主程序
使用最强反检测技术和多策略并行爬取
"""

import asyncio
import argparse
import sys
from pathlib import Path

from data_models import CrawlerConfig
from advanced_crawler import AdvancedBossCrawler, advanced_crawl_boss_jobs
from utils import Logger, format_job_summary


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="高级BOSS直聘职位信息爬虫 - 深度优化版",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
高级功能特性:
  🚀 超强反检测技术 - 完全绕过自动化检测
  🎯 多策略并行爬取 - 网络分析、API调用、深度爬取、移动端
  🧠 智能行为模拟 - 真实用户操作模拟
  📡 网络请求拦截 - 分析和调用真实API
  🔄 自动去重清洗 - 确保数据质量
  
使用示例:
  python advanced_main.py                                    # 默认高级爬取
  python advanced_main.py --keyword "Java开发" --location "上海"  # 指定条件
  python advanced_main.py --pages 10 --format excel          # 深度爬取
  python advanced_main.py --stealth-mode --proxy http://proxy:port  # 隐身模式
        """
    )
    
    parser.add_argument(
        "--keyword", "-k",
        type=str,
        default="Python",
        help="搜索关键词 (默认: Python)"
    )
    
    parser.add_argument(
        "--location", "-l", 
        type=str,
        default="北京",
        help="搜索地点 (默认: 北京)"
    )
    
    parser.add_argument(
        "--pages", "-p",
        type=int,
        default=5,
        help="爬取页数 (默认: 5)"
    )
    
    parser.add_argument(
        "--format", "-f",
        type=str,
        choices=["json", "csv", "excel"],
        default="json",
        help="输出格式 (默认: json)"
    )
    
    parser.add_argument(
        "--output", "-o",
        type=str,
        help="输出文件名 (默认: 根据格式自动生成)"
    )
    
    parser.add_argument(
        "--headless",
        type=str,
        choices=["true", "false"],
        default="true",
        help="是否无头模式运行 (默认: true)"
    )
    
    parser.add_argument(
        "--stealth-mode",
        action="store_true",
        help="启用隐身模式 (最强反检测)"
    )
    
    parser.add_argument(
        "--timeout",
        type=int,
        default=180,
        help="页面超时时间(秒) (默认: 180)"
    )
    
    parser.add_argument(
        "--proxy",
        type=str,
        help="代理服务器地址 (格式: http://host:port)"
    )
    
    parser.add_argument(
        "--max-retries",
        type=int,
        default=5,
        help="最大重试次数 (默认: 5)"
    )
    
    parser.add_argument(
        "--delay",
        type=float,
        default=8.0,
        help="请求间隔时间(秒) (默认: 8.0)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="显示详细日志"
    )
    
    parser.add_argument(
        "--force",
        action="store_true",
        help="强制模式 - 使用所有可用技术手段"
    )
    
    return parser.parse_args()


def generate_output_filename(keyword: str, location: str, format_type: str) -> str:
    """生成输出文件名"""
    from datetime import datetime
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    safe_keyword = "".join(c for c in keyword if c.isalnum() or c in "._-")
    safe_location = "".join(c for c in location if c.isalnum() or c in "._-")
    
    extensions = {
        "json": ".json",
        "csv": ".csv", 
        "excel": ".xlsx"
    }
    
    ext = extensions.get(format_type, ".json")
    return f"advanced_boss_{safe_keyword}_{safe_location}_{timestamp}{ext}"


async def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        # 生成输出文件名
        if not args.output:
            output_file = generate_output_filename(args.keyword, args.location, args.format)
        else:
            output_file = args.output
        
        # 创建高级爬虫配置
        config = CrawlerConfig(
            search_keyword=args.keyword,
            search_location=args.location,
            max_pages=args.pages,
            headless=(args.headless.lower() == "true"),
            timeout=args.timeout,
            use_proxy=bool(args.proxy),
            proxy_url=args.proxy,
            output_format=args.format,
            output_file=output_file,
            delay_range=(args.delay, args.delay + 5),
            max_retries=args.max_retries
        )
        
        # 显示爬取配置
        Logger.info("🚀 高级BOSS直聘爬虫启动")
        Logger.info("=" * 50)
        Logger.info("📋 爬取配置:")
        Logger.info(f"   🎯 关键词: {config.search_keyword}")
        Logger.info(f"   📍 地点: {config.search_location}")
        Logger.info(f"   📄 页数: {config.max_pages}")
        Logger.info(f"   💾 输出格式: {config.output_format}")
        Logger.info(f"   📁 输出文件: {config.output_file}")
        Logger.info(f"   👻 无头模式: {config.headless}")
        Logger.info(f"   ⏱️ 超时时间: {config.timeout}秒")
        Logger.info(f"   🔄 最大重试: {config.max_retries}次")
        Logger.info(f"   ⏰ 延时范围: {config.delay_range[0]}-{config.delay_range[1]}秒")
        
        if config.use_proxy:
            Logger.info(f"   🌐 代理服务器: {config.proxy_url}")
        
        if args.stealth_mode:
            Logger.info("   🥷 隐身模式: 已启用")
        
        if args.force:
            Logger.info("   💪 强制模式: 已启用")
        
        Logger.info("=" * 50)
        
        # 创建输出目录
        output_path = Path(config.output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 运行高级爬虫
        Logger.info("🎯 开始执行高级多策略爬取...")
        
        async with AdvancedBossCrawler(config) as crawler:
            result = await crawler.run_advanced_crawler()
            
            # 显示结果摘要
            if result.jobs:
                Logger.success("🎉 高级爬取完成!")
                Logger.info("=" * 50)
                print("\n" + format_job_summary(result.jobs))
                
                # 显示详细统计
                Logger.info("📊 详细统计:")
                Logger.info(f"   总职位数: {len(result.jobs)}")
                
                # 统计薪资分布
                salary_ranges = [job.salary_range for job in result.jobs if job.salary_range]
                if salary_ranges:
                    Logger.info(f"   有薪资信息: {len(salary_ranges)} 个")
                
                # 统计公司分布
                companies = list(set([job.company_name for job in result.jobs if job.company_name]))
                if companies:
                    Logger.info(f"   涉及公司: {len(companies)} 家")
                
                # 统计地区分布
                locations = list(set([job.location for job in result.jobs if job.location]))
                if locations:
                    Logger.info(f"   工作地点: {len(locations)} 个")
                
                # 显示文件信息
                if output_path.exists():
                    file_size = output_path.stat().st_size
                    Logger.info(f"📁 输出文件: {config.output_file} ({file_size:,} bytes)")
                
                Logger.info("=" * 50)
                Logger.success("✨ 所有任务完成!")
                
            else:
                Logger.warning("⚠️ 未获取到任何职位信息")
                Logger.info("💡 建议:")
                Logger.info("   1. 检查网络连接")
                Logger.info("   2. 尝试使用代理服务器")
                Logger.info("   3. 调整搜索关键词")
                Logger.info("   4. 增加延时时间")
                
    except KeyboardInterrupt:
        Logger.warning("⏹️ 用户中断爬取")
        sys.exit(1)
    except Exception as e:
        Logger.error(f"💥 程序运行出错: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def quick_advanced_demo():
    """快速高级演示函数"""
    async def demo():
        Logger.info("🎯 运行高级爬虫演示...")
        Logger.info("🚀 使用最强反检测技术和多策略并行爬取")
        
        # 使用高级便捷函数
        result = await advanced_crawl_boss_jobs(
            keyword="Python开发",
            location="北京", 
            max_pages=3,
            output_format="json",
            output_file="advanced_demo_jobs.json",
            headless=True
        )
        
        if result.jobs:
            print(f"\n✨ 高级演示完成! 获取到 {len(result.jobs)} 个职位")
            print("🎯 前5个职位:")
            for i, job in enumerate(result.jobs[:5], 1):
                print(f"{i}. {job.job_title} - {job.company_name} - {job.salary_range}")
            
            print(f"\n📁 数据已保存到: advanced_demo_jobs.json")
        else:
            print("❌ 高级演示失败，未获取到职位信息")
            print("💡 这可能是由于网站的强反爬虫机制，建议:")
            print("   1. 使用代理服务器")
            print("   2. 增加延时时间")
            print("   3. 尝试不同的搜索关键词")
    
    asyncio.run(demo())


if __name__ == "__main__":
    # 检查是否为演示模式
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        quick_advanced_demo()
    else:
        asyncio.run(main())

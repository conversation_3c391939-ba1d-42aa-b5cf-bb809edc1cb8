"""
配置文件
包含爬虫的各种配置参数
"""

import os
from typing import Dict, List

# BOSS直聘网站配置
BOSS_CONFIG = {
    "base_url": "https://www.zhipin.com",
    "search_url": "https://www.zhipin.com/web/geek/job",
    "job_detail_base": "https://www.zhipin.com/job_detail",
}

# 浏览器配置
BROWSER_CONFIG = {
    "headless": True,
    "viewport_width": 1920,
    "viewport_height": 1080,
    "java_script_enabled": True,
    "text_mode": False,
}

# 用户代理配置
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
]

# 请求头配置
DEFAULT_HEADERS = {
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
    "Accept-Encoding": "gzip, deflate, br",
    "DNT": "1",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "Sec-Fetch-Dest": "document",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "none",
    "Cache-Control": "max-age=0",
}

# 优化的数据提取CSS选择器配置
CSS_SELECTORS = {
    "job_list": {
        "name": "BOSS直聘职位列表",
        "baseSelector": ".job-list li, .job-card-wrapper, [class*='job-card'], [class*='job-item'], .job-primary",
        "fields": [
            {
                "name": "job_title",
                "selector": ".job-name a, .job-title, [class*='job-name'], [class*='job-title'], h3, h4",
                "type": "text"
            },
            {
                "name": "salary_range",
                "selector": ".salary, .red, [class*='salary'], [class*='money'], .price",
                "type": "text"
            },
            {
                "name": "company_name",
                "selector": ".company-name a, .company-text, [class*='company-name'], [class*='company-text']",
                "type": "text"
            },
            {
                "name": "job_detail_url",
                "selector": ".job-name a, a[href*='job_detail'], a[href*='job/']",
                "type": "attribute",
                "attribute": "href"
            },
            {
                "name": "location",
                "selector": ".job-area, .area, [class*='area'], [class*='location'], .job-location",
                "type": "text"
            },
            {
                "name": "work_experience",
                "selector": ".job-limit .limit-item:first-child, [class*='experience'], .job-require",
                "type": "text"
            },
            {
                "name": "education",
                "selector": ".job-limit .limit-item:last-child, [class*='degree'], [class*='education']",
                "type": "text"
            },
            {
                "name": "company_scale",
                "selector": ".company-tag-list .company-tag-item:first-child, [class*='scale']",
                "type": "text"
            },
            {
                "name": "company_industry",
                "selector": ".company-tag-list .company-tag-item:last-child, [class*='industry']",
                "type": "text"
            },
            {
                "name": "hr_info",
                "selector": ".recruiter-info, [class*='recruiter'], [class*='hr'], .boss-info",
                "type": "text"
            },
            {
                "name": "publish_time",
                "selector": ".job-pub-time, [class*='time'], [class*='publish'], .publish-time",
                "type": "text"
            }
        ]
    },
    "job_detail": {
        "name": "职位详情页",
        "baseSelector": ".job-detail-section",
        "fields": [
            {
                "name": "job_description",
                "selector": ".job-detail-text",
                "type": "text"
            },
            {
                "name": "job_requirements",
                "selector": ".job-require",
                "type": "text"
            },
            {
                "name": "welfare_benefits",
                "selector": ".job-welfare",
                "type": "text"
            }
        ]
    }
}

# 爬虫延时配置 - 增加延时应对反爬虫
DELAY_CONFIG = {
    "min_delay": 5,
    "max_delay": 10,
    "page_delay": 8,
    "detail_delay": 3,
}

# 重试配置
RETRY_CONFIG = {
    "max_retries": 3,
    "retry_delay": 10,
    "timeout": 120,
}

# 输出配置
OUTPUT_CONFIG = {
    "json_file": "boss_jobs.json",
    "csv_file": "boss_jobs.csv",
    "excel_file": "boss_jobs.xlsx",
    "encoding": "utf-8",
}

# 搜索参数映射
SEARCH_PARAMS = {
    "query": "query",           # 搜索关键词
    "city": "city",             # 城市代码
    "position": "position",     # 职位类别
    "experience": "experience", # 工作经验
    "degree": "degree",         # 学历要求
    "scale": "scale",           # 公司规模
    "salary": "salary",         # 薪资范围
    "page": "page",             # 页码
}

# 城市代码映射（部分主要城市）
CITY_CODES = {
    "北京": "101010100",
    "上海": "101020100", 
    "广州": "101280100",
    "深圳": "101280600",
    "杭州": "101210100",
    "南京": "101190100",
    "武汉": "101200100",
    "成都": "101270100",
    "西安": "101110100",
    "重庆": "101040100",
}

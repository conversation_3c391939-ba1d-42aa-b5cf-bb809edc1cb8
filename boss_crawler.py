"""
BOSS直聘职位信息爬虫
基于crawl4ai框架的高性能爬虫实现
"""

import json
import asyncio
import random
from typing import List, Optional, Dict, Any
from urllib.parse import urljoin, urlparse

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy

from data_models import JobPosition, JobSearchResult, CrawlerConfig
from config import BOSS_CONFIG, BROWSER_CONFIG, CSS_SELECTORS, DELAY_CONFIG, RETRY_CONFIG
from utils import RequestHelper, DataCleaner, Logger, DataExporter


class BossJobCrawler:
    """BOSS直聘职位爬虫主类"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.browser_config = None
        self.crawler = None
        self.total_crawled = 0
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def initialize(self):
        """初始化爬虫"""
        Logger.info("正在初始化BOSS直聘爬虫...")
        
        # 配置浏览器 - 使用更强的反检测策略
        self.browser_config = BrowserConfig(
            headless=self.config.headless,
            viewport_width=BROWSER_CONFIG["viewport_width"],
            viewport_height=BROWSER_CONFIG["viewport_height"],
            java_script_enabled=BROWSER_CONFIG["java_script_enabled"],
            text_mode=BROWSER_CONFIG["text_mode"],
            proxy=self.config.proxy_url if self.config.use_proxy else None,
            user_agent=RequestHelper.get_random_user_agent(),
            headers=RequestHelper.get_random_headers(),
            # 添加反检测参数
            accept_downloads=False,
            ignore_https_errors=True,
            java_script_enabled=True,
            extra_args=[
                "--disable-blink-features=AutomationControlled",
                "--disable-dev-shm-usage",
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-gpu",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-features=TranslateUI",
                "--disable-ipc-flooding-protection"
            ]
        )
        
        # 创建爬虫实例
        self.crawler = AsyncWebCrawler(config=self.browser_config)
        await self.crawler.__aenter__()
        
        Logger.success("爬虫初始化完成")
    
    async def close(self):
        """关闭爬虫"""
        if self.crawler:
            await self.crawler.__aexit__(None, None, None)
            Logger.info("爬虫已关闭")
    
    async def crawl_job_list(self, keyword: str, location: str, max_pages: int = 5) -> JobSearchResult:
        """爬取职位列表"""
        Logger.info(f"开始爬取职位: 关键词='{keyword}', 地点='{location}', 最大页数={max_pages}")
        
        all_jobs = []
        total_count = 0
        
        for page in range(1, max_pages + 1):
            Logger.info(f"正在爬取第 {page} 页...")
            
            try:
                # 构建搜索URL
                search_url = RequestHelper.build_search_url(
                    BOSS_CONFIG["base_url"], keyword, location, page
                )
                
                # 爬取页面
                jobs = await self._crawl_single_page(search_url, page)
                
                if not jobs:
                    Logger.warning(f"第 {page} 页未获取到职位信息，可能已到最后一页")
                    break
                
                all_jobs.extend(jobs)
                Logger.success(f"第 {page} 页获取到 {len(jobs)} 个职位")
                
                # 页面间延时
                await RequestHelper.random_delay(
                    DELAY_CONFIG["page_delay"], 
                    DELAY_CONFIG["page_delay"] + 2
                )
                
            except Exception as e:
                Logger.error(f"爬取第 {page} 页时出错: {e}")
                continue
        
        # 创建搜索结果
        result = JobSearchResult(
            total_count=len(all_jobs),
            current_page=max_pages,
            jobs=all_jobs,
            search_keyword=keyword,
            search_location=location
        )
        
        Logger.success(f"职位列表爬取完成，共获取 {len(all_jobs)} 个职位")
        return result
    
    async def _crawl_single_page(self, url: str, page: int) -> List[JobPosition]:
        """爬取单个页面的职位信息"""
        jobs = []
        
        for attempt in range(RETRY_CONFIG["max_retries"]):
            try:
                # 配置提取策略
                extraction_strategy = JsonCssExtractionStrategy(
                    CSS_SELECTORS["job_list"], 
                    verbose=False
                )
                
                # 配置爬取参数 - 增强反检测
                run_config = CrawlerRunConfig(
                    cache_mode=CacheMode.BYPASS,
                    extraction_strategy=extraction_strategy,
                    page_timeout=self.config.timeout * 1000,
                    delay_before_return_html=8.0,  # 增加等待时间
                    js_code=[
                        # 反检测和页面加载脚本
                        """
                        // 移除webdriver标识
                        Object.defineProperty(navigator, 'webdriver', {
                            get: () => undefined,
                        });

                        // 模拟真实用户行为
                        await new Promise(resolve => {
                            // 随机鼠标移动
                            const moveEvent = new MouseEvent('mousemove', {
                                clientX: Math.random() * window.innerWidth,
                                clientY: Math.random() * window.innerHeight
                            });
                            document.dispatchEvent(moveEvent);

                            // 等待一段时间
                            setTimeout(() => {
                                // 滚动页面
                                let totalHeight = 0;
                                const distance = 100;
                                const timer = setInterval(() => {
                                    const scrollHeight = document.body.scrollHeight;
                                    window.scrollBy(0, distance);
                                    totalHeight += distance;

                                    if(totalHeight >= scrollHeight){
                                        clearInterval(timer);
                                        resolve();
                                    }
                                }, 300);
                            }, 2000);
                        });
                        """
                    ]
                )
                
                # 执行爬取
                result = await self.crawler.arun(url=url, config=run_config)
                
                if not result.success:
                    raise Exception(f"爬取失败: {result.error_message}")
                
                # 解析提取的数据
                if result.extracted_content:
                    raw_jobs = json.loads(result.extracted_content)
                    
                    for job_data in raw_jobs:
                        # 数据清洗
                        cleaned_data = DataCleaner.clean_job_data(job_data)
                        
                        # 处理详情页URL
                        if cleaned_data.get("job_detail_url"):
                            detail_url = cleaned_data["job_detail_url"]
                            if detail_url.startswith("/"):
                                cleaned_data["job_detail_url"] = urljoin(BOSS_CONFIG["base_url"], detail_url)
                        
                        # 创建职位对象
                        try:
                            job = JobPosition(**cleaned_data)
                            jobs.append(job)
                        except Exception as e:
                            Logger.warning(f"创建职位对象失败: {e}, 数据: {cleaned_data}")
                            continue
                
                break  # 成功则跳出重试循环
                
            except Exception as e:
                Logger.warning(f"第 {attempt + 1} 次尝试失败: {e}")
                if attempt < RETRY_CONFIG["max_retries"] - 1:
                    await asyncio.sleep(RETRY_CONFIG["retry_delay"])
                else:
                    Logger.error(f"页面 {page} 爬取失败，已达最大重试次数")
        
        return jobs
    
    async def crawl_job_details(self, jobs: List[JobPosition]) -> List[JobPosition]:
        """爬取职位详情信息"""
        Logger.info(f"开始爬取 {len(jobs)} 个职位的详情信息...")
        
        enhanced_jobs = []
        
        for i, job in enumerate(jobs, 1):
            try:
                Logger.info(f"正在爬取详情 ({i}/{len(jobs)}): {job.job_title}")
                
                # 爬取详情页
                detail_info = await self._crawl_job_detail(job.job_detail_url)
                
                # 更新职位信息
                if detail_info:
                    job.job_description = detail_info.get("job_description", job.job_description)
                    job.job_requirements = detail_info.get("job_requirements", job.job_requirements)
                    job.welfare_benefits = detail_info.get("welfare_benefits", job.welfare_benefits)
                
                enhanced_jobs.append(job)
                
                # 详情页间延时
                await RequestHelper.random_delay(
                    DELAY_CONFIG["detail_delay"],
                    DELAY_CONFIG["detail_delay"] + 1
                )
                
            except Exception as e:
                Logger.warning(f"爬取职位详情失败: {job.job_title}, 错误: {e}")
                enhanced_jobs.append(job)  # 即使失败也保留原始信息
                continue
        
        Logger.success(f"职位详情爬取完成，成功处理 {len(enhanced_jobs)} 个职位")
        return enhanced_jobs
    
    async def _crawl_job_detail(self, detail_url: str) -> Optional[Dict[str, Any]]:
        """爬取单个职位的详情信息"""
        if not detail_url:
            return None
        
        try:
            # 配置详情页提取策略
            extraction_strategy = JsonCssExtractionStrategy(
                CSS_SELECTORS["job_detail"],
                verbose=False
            )
            
            run_config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                extraction_strategy=extraction_strategy,
                page_timeout=self.config.timeout * 1000,
                delay_before_return_html=2.0
            )
            
            # 执行爬取
            result = await self.crawler.arun(url=detail_url, config=run_config)
            
            if result.success and result.extracted_content:
                detail_data = json.loads(result.extracted_content)
                if detail_data and len(detail_data) > 0:
                    return DataCleaner.clean_job_data(detail_data[0])
            
        except Exception as e:
            Logger.warning(f"详情页爬取异常: {e}")
        
        return None
    
    async def run_crawler(self) -> JobSearchResult:
        """运行完整的爬虫流程"""
        try:
            # 1. 爬取职位列表
            result = await self.crawl_job_list(
                self.config.search_keyword,
                self.config.search_location,
                self.config.max_pages
            )
            
            # 2. 爬取职位详情（可选）
            if result.jobs and len(result.jobs) > 0:
                Logger.info("开始爬取职位详情...")
                enhanced_jobs = await self.crawl_job_details(result.jobs)
                result.jobs = enhanced_jobs
            
            # 3. 导出数据
            await self._export_data(result)
            
            return result
            
        except Exception as e:
            Logger.error(f"爬虫运行失败: {e}")
            raise
    
    async def _export_data(self, result: JobSearchResult):
        """导出数据"""
        if not result.jobs:
            Logger.warning("没有数据可导出")
            return
        
        try:
            if self.config.output_format.lower() == "json":
                await DataExporter.export_to_json(result, self.config.output_file)
            elif self.config.output_format.lower() == "csv":
                DataExporter.export_to_csv(result, self.config.output_file.replace('.json', '.csv'))
            elif self.config.output_format.lower() == "excel":
                DataExporter.export_to_excel(result, self.config.output_file.replace('.json', '.xlsx'))
            else:
                # 默认导出JSON
                await DataExporter.export_to_json(result, self.config.output_file)
                
        except Exception as e:
            Logger.error(f"数据导出失败: {e}")


# 便捷函数
async def crawl_boss_jobs(
    keyword: str = "Python",
    location: str = "北京", 
    max_pages: int = 3,
    output_format: str = "json",
    output_file: str = "boss_jobs.json"
) -> JobSearchResult:
    """便捷的职位爬取函数"""
    
    config = CrawlerConfig(
        search_keyword=keyword,
        search_location=location,
        max_pages=max_pages,
        output_format=output_format,
        output_file=output_file
    )
    
    async with BossJobCrawler(config) as crawler:
        return await crawler.run_crawler()

#!/usr/bin/env python3
"""
终极BOSS直聘爬虫 - 运行脚本
集成所有最强技术，确保获取完整准确的职位信息
"""

import asyncio
import argparse
import sys
from pathlib import Path
from datetime import datetime

from data_models import CrawlerConfig
from ultimate_crawler import UltimateBossCrawler, ultimate_crawl_boss_jobs
from utils import Logger


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="终极BOSS直聘爬虫 - 使用所有可用技术手段",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
🚀 终极爬虫特性:
  🥷 超强反检测技术 - 完全绕过自动化检测
  🔄 多策略并行爬取 - 网络分析、API调用、深度爬取、移动端
  🧠 智能行为模拟 - 真实用户操作模拟
  📡 网络请求拦截 - 分析和调用真实API
  🔧 数据质量增强 - 自动清洗、验证、补全数据
  📊 智能去重算法 - 确保数据唯一性
  💾 多格式导出 - JSON、CSV、Excel

使用示例:
  python run_ultimate.py                                    # 默认设置
  python run_ultimate.py --keyword "Java开发" --location "上海"  # 自定义搜索
  python run_ultimate.py --pages 10 --format excel          # 深度爬取
  python run_ultimate.py --force-mode                       # 强制模式
        """
    )
    
    parser.add_argument("--keyword", "-k", default="Python", help="搜索关键词")
    parser.add_argument("--location", "-l", default="北京", help="搜索地点")
    parser.add_argument("--pages", "-p", type=int, default=5, help="爬取页数")
    parser.add_argument("--format", "-f", choices=["json", "csv", "excel"], default="json", help="输出格式")
    parser.add_argument("--output", "-o", help="输出文件名")
    parser.add_argument("--headless", choices=["true", "false"], default="true", help="无头模式")
    parser.add_argument("--force-mode", action="store_true", help="强制模式 - 使用所有技术手段")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细日志")
    
    args = parser.parse_args()
    
    # 生成输出文件名
    if not args.output:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_keyword = "".join(c for c in args.keyword if c.isalnum() or c in "._-")
        safe_location = "".join(c for c in args.location if c.isalnum() or c in "._-")
        ext = {"json": ".json", "csv": ".csv", "excel": ".xlsx"}[args.format]
        args.output = f"ultimate_{safe_keyword}_{safe_location}_{timestamp}{ext}"
    
    # 运行终极爬虫
    asyncio.run(run_ultimate_crawler(args))


async def run_ultimate_crawler(args):
    """运行终极爬虫"""
    try:
        Logger.info("🚀 启动终极BOSS直聘爬虫")
        Logger.info("=" * 80)
        Logger.info("💪 使用所有可用技术手段，确保获取完整准确的职位信息")
        Logger.info("=" * 80)
        
        if args.force_mode:
            Logger.info("⚡ 强制模式已启用 - 将使用最激进的爬取策略")
        
        # 创建配置
        config = CrawlerConfig(
            search_keyword=args.keyword,
            search_location=args.location,
            max_pages=args.pages,
            output_format=args.format,
            output_file=args.output,
            headless=(args.headless.lower() == "true"),
            timeout=300 if args.force_mode else 180,  # 强制模式使用更长超时
            delay_range=(5, 10) if args.force_mode else (8, 15),
            max_retries=10 if args.force_mode else 5
        )
        
        # 显示配置
        Logger.info("📋 爬取配置:")
        Logger.info(f"   🎯 关键词: {config.search_keyword}")
        Logger.info(f"   📍 地点: {config.search_location}")
        Logger.info(f"   📄 页数: {config.max_pages}")
        Logger.info(f"   💾 格式: {config.output_format}")
        Logger.info(f"   📁 文件: {config.output_file}")
        Logger.info(f"   ⏱️ 超时: {config.timeout}秒")
        Logger.info(f"   🔄 重试: {config.max_retries}次")
        
        # 运行终极爬虫
        async with UltimateBossCrawler(config) as crawler:
            result = await crawler.ultimate_crawl()
            
            if result.jobs:
                Logger.success("🎉 终极爬取成功完成!")
                print(f"\n📊 最终获取到 {len(result.jobs)} 个高质量职位")
                
                # 显示前几个职位
                print("\n🎯 职位预览:")
                for i, job in enumerate(result.jobs[:5], 1):
                    print(f"  {i}. {job.job_title}")
                    print(f"     公司: {job.company_name}")
                    print(f"     薪资: {job.salary_range}")
                    print(f"     地点: {job.location}")
                    if job.job_detail_url:
                        print(f"     链接: {job.job_detail_url}")
                    print()
                
                if len(result.jobs) > 5:
                    print(f"   ... 还有 {len(result.jobs) - 5} 个职位")
                
                print(f"\n📁 数据已保存到: {config.output_file}")
                
            else:
                Logger.warning("⚠️ 未获取到职位数据")
                print("\n💡 建议:")
                print("   1. 检查网络连接")
                print("   2. 尝试不同的搜索关键词")
                print("   3. 使用 --force-mode 参数")
                print("   4. 检查目标网站是否可访问")
        
    except KeyboardInterrupt:
        Logger.warning("⏹️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        Logger.error(f"💥 终极爬虫运行失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()

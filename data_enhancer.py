#!/usr/bin/env python3
"""
数据增强器 - 提升爬取数据的质量和完整性
"""

import re
import asyncio
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, urlparse

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
from data_models import JobPosition
from utils import Logger, DataCleaner


class DataEnhancer:
    """数据增强器"""
    
    def __init__(self):
        self.crawler = None
        self.salary_patterns = [
            r'(\d+)[kK][-~](\d+)[kK]',
            r'(\d+)[-~](\d+)[kK]',
            r'(\d+)[kK]\+',
            r'面议',
            r'薪资面议'
        ]
        
        self.experience_patterns = [
            r'(\d+)[-~](\d+)年',
            r'(\d+)年以上',
            r'不限',
            r'应届',
            r'实习'
        ]
        
        self.education_patterns = [
            r'本科',
            r'硕士',
            r'博士',
            r'大专',
            r'高中',
            r'不限'
        ]
    
    async def __aenter__(self):
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
    
    async def initialize(self):
        """初始化数据增强器"""
        Logger.info("🔧 初始化数据增强器...")
        
        browser_config = BrowserConfig(
            headless=True,
            viewport_width=1920,
            viewport_height=1080,
            java_script_enabled=True,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            extra_args=[
                "--disable-blink-features=AutomationControlled",
                "--disable-dev-shm-usage",
                "--no-sandbox"
            ]
        )
        
        self.crawler = AsyncWebCrawler(config=browser_config)
        await self.crawler.__aenter__()
        
        Logger.success("✅ 数据增强器初始化完成")
    
    async def close(self):
        """关闭数据增强器"""
        if self.crawler:
            await self.crawler.__aexit__(None, None, None)
            Logger.info("🔒 数据增强器已关闭")
    
    async def enhance_jobs(self, jobs: List[JobPosition]) -> List[JobPosition]:
        """增强职位数据"""
        Logger.info(f"🚀 开始增强 {len(jobs)} 个职位数据...")
        
        enhanced_jobs = []
        
        for i, job in enumerate(jobs, 1):
            Logger.info(f"🔧 增强职位 {i}/{len(jobs)}: {job.job_title}")
            
            try:
                # 1. 清洗和标准化基础数据
                enhanced_job = self._clean_basic_data(job)
                
                # 2. 提取和解析薪资信息
                enhanced_job = self._enhance_salary_info(enhanced_job)
                
                # 3. 标准化经验和学历要求
                enhanced_job = self._enhance_requirements(enhanced_job)
                
                # 4. 如果有详情页URL，尝试获取详细信息
                if enhanced_job.job_detail_url and enhanced_job.job_detail_url.startswith('http'):
                    enhanced_job = await self._enhance_with_detail_page(enhanced_job)
                
                # 5. 验证和修复数据
                enhanced_job = self._validate_and_fix_data(enhanced_job)
                
                enhanced_jobs.append(enhanced_job)
                
                # 延时避免请求过快
                await asyncio.sleep(1)
                
            except Exception as e:
                Logger.warning(f"⚠️ 增强职位 {job.job_title} 失败: {e}")
                enhanced_jobs.append(job)  # 保留原始数据
        
        Logger.success(f"✅ 数据增强完成，处理了 {len(enhanced_jobs)} 个职位")
        return enhanced_jobs
    
    def _clean_basic_data(self, job: JobPosition) -> JobPosition:
        """清洗基础数据"""
        # 创建新的职位对象
        cleaned_job = JobPosition(
            job_title=DataCleaner.clean_text(job.job_title),
            job_requirements=DataCleaner.clean_text(job.job_requirements),
            salary_range=DataCleaner.clean_salary(job.salary_range),
            company_name=DataCleaner.clean_text(job.company_name),
            company_scale=DataCleaner.clean_text(job.company_scale),
            company_industry=DataCleaner.clean_text(job.company_industry),
            job_detail_url=job.job_detail_url,
            location=DataCleaner.clean_text(job.location),
            work_experience=DataCleaner.clean_text(job.work_experience),
            education=DataCleaner.clean_text(job.education),
            job_description=DataCleaner.clean_text(job.job_description),
            welfare_benefits=DataCleaner.clean_text(job.welfare_benefits),
            hr_info=DataCleaner.clean_text(job.hr_info),
            publish_time=DataCleaner.clean_text(job.publish_time),
            crawl_time=job.crawl_time
        )
        
        return cleaned_job
    
    def _enhance_salary_info(self, job: JobPosition) -> JobPosition:
        """增强薪资信息"""
        salary_text = job.salary_range
        
        if not salary_text or salary_text in ['-K', '-K-薪', '薪资面议']:
            # 尝试从职位标题中提取薪资信息
            title_salary = self._extract_salary_from_text(job.job_title)
            if title_salary:
                job.salary_range = title_salary
            else:
                job.salary_range = "薪资面议"
        else:
            # 标准化薪资格式
            job.salary_range = self._standardize_salary(salary_text)
        
        return job
    
    def _extract_salary_from_text(self, text: str) -> Optional[str]:
        """从文本中提取薪资信息"""
        if not text:
            return None
        
        for pattern in self.salary_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(0)
        
        return None
    
    def _standardize_salary(self, salary: str) -> str:
        """标准化薪资格式"""
        if not salary:
            return "薪资面议"
        
        # 移除多余字符
        salary = re.sub(r'[^\d\-~kK\+万元]', '', salary)
        
        # 统一格式
        salary = salary.replace('k', 'K').replace('~', '-')
        
        # 如果没有K，可能是万元单位
        if 'K' not in salary and '万' in salary:
            salary = salary.replace('万', 'K')
        
        return salary if salary else "薪资面议"
    
    def _enhance_requirements(self, job: JobPosition) -> JobPosition:
        """增强要求信息"""
        # 从job_requirements中提取经验和学历
        requirements_text = f"{job.job_requirements} {job.work_experience} {job.education}"
        
        # 提取工作经验
        if not job.work_experience or job.work_experience == "":
            experience = self._extract_experience(requirements_text)
            if experience:
                job.work_experience = experience
        
        # 提取学历要求
        if not job.education or job.education == "":
            education = self._extract_education(requirements_text)
            if education:
                job.education = education
        
        return job
    
    def _extract_experience(self, text: str) -> Optional[str]:
        """提取工作经验"""
        if not text:
            return None
        
        for pattern in self.experience_patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(0)
        
        return None
    
    def _extract_education(self, text: str) -> Optional[str]:
        """提取学历要求"""
        if not text:
            return None
        
        for pattern in self.education_patterns:
            if re.search(pattern, text):
                return pattern.strip('r')
        
        return None
    
    async def _enhance_with_detail_page(self, job: JobPosition) -> JobPosition:
        """通过详情页增强数据"""
        try:
            Logger.info(f"📄 获取详情页: {job.job_title}")
            
            run_config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                page_timeout=30000,
                delay_before_return_html=3.0
            )
            
            result = await self.crawler.arun(url=job.job_detail_url, config=run_config)
            
            if result.success:
                # 从详情页提取更多信息
                html = result.html
                
                # 提取职位描述
                if not job.job_description:
                    job_desc = self._extract_job_description(html)
                    if job_desc:
                        job.job_description = job_desc
                
                # 提取福利待遇
                if not job.welfare_benefits:
                    welfare = self._extract_welfare_benefits(html)
                    if welfare:
                        job.welfare_benefits = welfare
                
                # 提取公司信息
                if not job.company_scale:
                    company_info = self._extract_company_info(html)
                    if company_info.get('scale'):
                        job.company_scale = company_info['scale']
                    if company_info.get('industry'):
                        job.company_industry = company_info['industry']
                
                Logger.success(f"✅ 详情页增强完成: {job.job_title}")
            else:
                Logger.warning(f"⚠️ 详情页访问失败: {job.job_title}")
                
        except Exception as e:
            Logger.warning(f"⚠️ 详情页增强异常: {e}")
        
        return job
    
    def _extract_job_description(self, html: str) -> Optional[str]:
        """从HTML中提取职位描述"""
        # 使用正则表达式提取职位描述
        patterns = [
            r'<div[^>]*class="[^"]*job-detail[^"]*"[^>]*>(.*?)</div>',
            r'<div[^>]*class="[^"]*job-sec[^"]*"[^>]*>(.*?)</div>',
            r'<div[^>]*class="[^"]*text[^"]*"[^>]*>(.*?)</div>'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html, re.DOTALL | re.IGNORECASE)
            if matches:
                # 清理HTML标签
                desc = re.sub(r'<[^>]+>', '', matches[0])
                desc = DataCleaner.clean_text(desc)
                if len(desc) > 50:  # 确保是有意义的描述
                    return desc
        
        return None
    
    def _extract_welfare_benefits(self, html: str) -> Optional[str]:
        """从HTML中提取福利待遇"""
        patterns = [
            r'<div[^>]*class="[^"]*welfare[^"]*"[^>]*>(.*?)</div>',
            r'<div[^>]*class="[^"]*benefit[^"]*"[^>]*>(.*?)</div>',
            r'<span[^>]*class="[^"]*tag[^"]*"[^>]*>(.*?)</span>'
        ]
        
        benefits = []
        for pattern in patterns:
            matches = re.findall(pattern, html, re.DOTALL | re.IGNORECASE)
            for match in matches:
                benefit = re.sub(r'<[^>]+>', '', match)
                benefit = DataCleaner.clean_text(benefit)
                if benefit and len(benefit) > 2:
                    benefits.append(benefit)
        
        return ', '.join(benefits[:5]) if benefits else None
    
    def _extract_company_info(self, html: str) -> Dict[str, str]:
        """从HTML中提取公司信息"""
        info = {}
        
        # 提取公司规模
        scale_patterns = [
            r'<span[^>]*>(\d+[-~]\d+人)</span>',
            r'<span[^>]*>(少于\d+人)</span>',
            r'<span[^>]*>(\d+人以上)</span>'
        ]
        
        for pattern in scale_patterns:
            match = re.search(pattern, html)
            if match:
                info['scale'] = match.group(1)
                break
        
        # 提取行业信息
        industry_patterns = [
            r'<span[^>]*class="[^"]*industry[^"]*"[^>]*>(.*?)</span>',
            r'<div[^>]*class="[^"]*industry[^"]*"[^>]*>(.*?)</div>'
        ]
        
        for pattern in industry_patterns:
            match = re.search(pattern, html, re.IGNORECASE)
            if match:
                industry = re.sub(r'<[^>]+>', '', match.group(1))
                industry = DataCleaner.clean_text(industry)
                if industry:
                    info['industry'] = industry
                    break
        
        return info
    
    def _validate_and_fix_data(self, job: JobPosition) -> JobPosition:
        """验证和修复数据"""
        # 确保必要字段不为空
        if not job.job_title or job.job_title.strip() == "":
            job.job_title = "职位信息待完善"
        
        if not job.company_name or job.company_name.strip() == "":
            job.company_name = "公司信息待完善"
        
        if not job.salary_range or job.salary_range.strip() == "":
            job.salary_range = "薪资面议"
        
        # 修复URL
        if job.job_detail_url and not job.job_detail_url.startswith('http'):
            if job.job_detail_url.startswith('/'):
                job.job_detail_url = urljoin("https://www.zhipin.com", job.job_detail_url)
        
        # 标准化地点信息
        if job.location:
            job.location = re.sub(r'[·\s]+', '·', job.location)
        
        return job


async def enhance_job_data(jobs: List[JobPosition]) -> List[JobPosition]:
    """增强职位数据的便捷函数"""
    async with DataEnhancer() as enhancer:
        return await enhancer.enhance_jobs(jobs)

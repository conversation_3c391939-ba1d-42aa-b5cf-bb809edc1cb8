#!/usr/bin/env python3
"""
爬虫进度监控器
实时监控爬虫运行状态和进度
"""

import time
import json
import os
from pathlib import Path
from datetime import datetime


def monitor_crawler_progress():
    """监控爬虫进度"""
    print("🔍 爬虫进度监控器启动")
    print("=" * 50)
    
    start_time = datetime.now()
    last_file_size = 0
    last_check_time = start_time
    
    # 查找最新的输出文件
    output_files = []
    for pattern in ["ultimate_*.json", "advanced_*.json", "*boss*.json"]:
        output_files.extend(Path(".").glob(pattern))
    
    if output_files:
        # 按修改时间排序，获取最新文件
        latest_file = max(output_files, key=lambda f: f.stat().st_mtime)
        print(f"📁 监控文件: {latest_file}")
    else:
        print("⚠️ 未找到输出文件，等待爬虫开始...")
        latest_file = None
    
    print("=" * 50)
    
    while True:
        try:
            current_time = datetime.now()
            elapsed = (current_time - start_time).total_seconds()
            
            # 检查文件变化
            if latest_file and latest_file.exists():
                current_size = latest_file.stat().st_size
                
                if current_size != last_file_size:
                    print(f"\n⏰ {current_time.strftime('%H:%M:%S')} | 运行时间: {elapsed:.0f}秒")
                    print(f"📄 文件大小: {current_size:,} bytes (+{current_size - last_file_size:,})")
                    
                    # 尝试读取并分析文件内容
                    try:
                        if current_size > 100:  # 文件有一定大小才尝试读取
                            with open(latest_file, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                                
                            if 'jobs' in data:
                                job_count = len(data['jobs'])
                                print(f"🎯 已获取职位: {job_count} 个")
                                
                                if job_count > 0:
                                    # 显示最新职位
                                    latest_job = data['jobs'][-1]
                                    print(f"📋 最新职位: {latest_job.get('job_title', 'N/A')} - {latest_job.get('company_name', 'N/A')}")
                                
                                # 计算爬取速度
                                time_diff = (current_time - last_check_time).total_seconds()
                                if time_diff > 0:
                                    speed = (current_size - last_file_size) / time_diff
                                    print(f"📈 写入速度: {speed:.1f} bytes/秒")
                    
                    except (json.JSONDecodeError, KeyError):
                        print("📝 文件正在写入中...")
                    
                    last_file_size = current_size
                    last_check_time = current_time
                    print("-" * 30)
            
            else:
                # 重新查找文件
                output_files = []
                for pattern in ["ultimate_*.json", "advanced_*.json", "*boss*.json"]:
                    output_files.extend(Path(".").glob(pattern))
                
                if output_files and not latest_file:
                    latest_file = max(output_files, key=lambda f: f.stat().st_mtime)
                    print(f"📁 发现新文件: {latest_file}")
                elif not output_files:
                    print(f"⏳ {current_time.strftime('%H:%M:%S')} | 等待爬虫开始... ({elapsed:.0f}秒)")
            
            time.sleep(5)  # 每5秒检查一次
            
        except KeyboardInterrupt:
            print("\n⏹️ 监控已停止")
            break
        except Exception as e:
            print(f"❌ 监控错误: {e}")
            time.sleep(5)


def show_final_summary():
    """显示最终摘要"""
    print("\n" + "=" * 60)
    print("📊 最终摘要")
    print("=" * 60)
    
    # 查找所有输出文件
    output_files = []
    for pattern in ["ultimate_*.json", "advanced_*.json", "*boss*.json"]:
        output_files.extend(Path(".").glob(pattern))
    
    if not output_files:
        print("❌ 未找到任何输出文件")
        return
    
    for file_path in sorted(output_files, key=lambda f: f.stat().st_mtime, reverse=True):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            file_size = file_path.stat().st_size
            mod_time = datetime.fromtimestamp(file_path.stat().st_mtime)
            
            print(f"\n📁 文件: {file_path}")
            print(f"   📏 大小: {file_size:,} bytes")
            print(f"   ⏰ 修改时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            if 'meta' in data:
                meta = data['meta']
                print(f"   🎯 搜索词: {meta.get('search_keyword', 'N/A')}")
                print(f"   📍 地点: {meta.get('search_location', 'N/A')}")
                print(f"   📊 职位数: {meta.get('job_count', len(data.get('jobs', [])))}")
            elif 'jobs' in data:
                print(f"   📊 职位数: {len(data['jobs'])}")
            
            # 显示前几个职位
            jobs = data.get('jobs', [])
            if jobs:
                print(f"   📋 职位预览:")
                for i, job in enumerate(jobs[:3], 1):
                    title = job.get('job_title', 'N/A')
                    company = job.get('company_name', 'N/A')
                    salary = job.get('salary_range', 'N/A')
                    print(f"      {i}. {title} - {company} - {salary}")
                
                if len(jobs) > 3:
                    print(f"      ... 还有 {len(jobs) - 3} 个职位")
        
        except Exception as e:
            print(f"❌ 读取文件 {file_path} 失败: {e}")
    
    print("=" * 60)


if __name__ == "__main__":
    try:
        monitor_crawler_progress()
    finally:
        show_final_summary()
